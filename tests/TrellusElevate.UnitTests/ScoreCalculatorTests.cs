using TrellusElevate.Application.Features.Assessments.Helpers;

namespace TrellusElevate.UnitTests;

public class ScoreCalculatorTests
{
    [Fact]
    public void ValidateFormula_WithValidFormula_ShouldReturnSuccess()
    {
        // Arrange
        var formula = "Q1 + Q2";
        var availableParameters = new[] { "Q1", "Q2" };

        // Act
        var result = ScoreCalculatorHelper.ValidateFormula(formula, availableParameters);

        // Assert
        Assert.True(result.IsOk);
    }

    [Fact]
    public void ValidateFormula_WithUndefinedParameters_ShouldReturnError()
    {
        // Arrange
        var formula = "Q1 + Q3"; // Q3 is not available
        var availableParameters = new[] { "Q1", "Q2" };

        // Act
        var result = ScoreCalculatorHelper.ValidateFormula(formula, availableParameters);

        // Assert
        Assert.True(result.IsError);
        Assert.Contains("undefined parameters", result.Error.Description.ToLower());
    }

    [Fact]
    public void ValidateFormula_WithInvalidSyntax_ShouldReturnError()
    {
        // Arrange
        var formula = "Q1 + + Q2"; // Invalid syntax
        var availableParameters = new[] { "Q1", "Q2" };

        // Act
        var result = ScoreCalculatorHelper.ValidateFormula(formula, availableParameters);

        // Assert
        Assert.True(result.IsError);
        Assert.Contains("syntax", result.Error.Description.ToLower());
    }

    [Fact]
    public void ValidateFormula_WithEmptyFormula_ShouldReturnSuccess()
    {
        // Arrange
        var formula = "";
        var availableParameters = new[] { "Q1", "Q2" };

        // Act
        var result = ScoreCalculatorHelper.ValidateFormula(formula, availableParameters);

        // Assert
        Assert.True(result.IsOk);
    }

    [Fact]
    public void ValidateFormula_WithComplexFormula_ShouldReturnSuccess()
    {
        // Arrange
        var formula = "(Q1 * 2) + (Q2 / 3) - Q3";
        var availableParameters = new[] { "Q1", "Q2", "Q3" };

        // Act
        var result = ScoreCalculatorHelper.ValidateFormula(formula, availableParameters);

        // Assert
        Assert.True(result.IsOk);
    }

    [Fact]
    public void ValidateFormula_WithMathematicalFunctions_ShouldReturnSuccess()
    {
        // Arrange
        var formula = "sqrt(Q1) + abs(Q2)";
        var availableParameters = new[] { "Q1", "Q2" };

        // Act
        var result = ScoreCalculatorHelper.ValidateFormula(formula, availableParameters);

        // Assert
        Assert.True(result.IsOk);
    }
}
