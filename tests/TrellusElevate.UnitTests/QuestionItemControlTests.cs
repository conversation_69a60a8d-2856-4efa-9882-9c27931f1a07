using TrellusElevate.Application.Features.QuestionnaireResponses.Models;
using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.UnitTests;

public class QuestionItemControlTests
{
    [Fact]
    public void MultiSelectDisplayText_WithNoSelection_ShouldReturnEmpty()
    {
        // Arrange
        var questionItem = CreateQuestionItemWithOptions();
        var selectedValues = new List<QuestionAnswer>();

        // Act
        var result = GetMultiSelectDisplayText(selectedValues, questionItem);

        // Assert
        Assert.Equal("", result);
    }

    [Fact]
    public void MultiSelectDisplayText_WithSingleSelection_ShouldReturnTitle()
    {
        // Arrange
        var questionItem = CreateQuestionItemWithOptions();
        var selectedValues = new List<QuestionAnswer>
        {
            new QuestionAnswer("option1", AnswerType.String)
        };

        // Act
        var result = GetMultiSelectDisplayText(selectedValues, questionItem);

        // Assert
        Assert.Equal("Option 1", result);
    }

    [Fact]
    public void MultiSelectDisplayText_WithMultipleSelections_ShouldReturnCount()
    {
        // Arrange
        var questionItem = CreateQuestionItemWithOptions();
        var selectedValues = new List<QuestionAnswer>
        {
            new QuestionAnswer("option1", AnswerType.String),
            new QuestionAnswer("option2", AnswerType.String)
        };

        // Act
        var result = GetMultiSelectDisplayText(selectedValues, questionItem);

        // Assert
        Assert.Equal("2 selected", result);
    }

    [Fact]
    public void MultiSelectDisplayText_WithUnknownSelection_ShouldIgnoreUnknown()
    {
        // Arrange
        var questionItem = CreateQuestionItemWithOptions();
        var selectedValues = new List<QuestionAnswer>
        {
            new QuestionAnswer("option1", AnswerType.String),
            new QuestionAnswer("unknown", AnswerType.String) // This option doesn't exist
        };

        // Act
        var result = GetMultiSelectDisplayText(selectedValues, questionItem);

        // Assert
        Assert.Equal("Option 1", result);
    }

    private static QuestionnaireItemDto CreateQuestionItemWithOptions()
    {
        return new QuestionnaireItemDto
        {
            Id = Guid.NewGuid(),
            Text = "Test Question",
            Type = QuestionType.String,
            Repeats = true,
            Options = new List<AnswerOption>
            {
                new AnswerOption(new QuestionAnswer("option1", AnswerType.String), "Option 1", false),
                new AnswerOption(new QuestionAnswer("option2", AnswerType.String), "Option 2", false),
                new AnswerOption(new QuestionAnswer("option3", AnswerType.String), "Option 3", false)
            }
        };
    }

    // Helper method that simulates the logic from QuestionItemControl
    private static string GetMultiSelectDisplayText(IEnumerable<QuestionAnswer> selectedValues, QuestionnaireItemDto item)
    {
        if (selectedValues == null || !selectedValues.Any())
            return "";

        var selectedTitles = selectedValues
            .Select(answer => item.Options.FirstOrDefault(option => option.Answer?.Equals(answer) == true)?.Title)
            .Where(title => !string.IsNullOrEmpty(title))
            .ToList();

        return selectedTitles.Count switch
        {
            0 => "",
            1 => selectedTitles.First()!,
            _ => $"{selectedTitles.Count} selected"
        };
    }
}
