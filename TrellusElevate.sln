
Microsoft Visual Studio Solution File, Format Version 12.00
#
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{FCCF4B6C-71BA-441C-A4D6-15D734B3F188}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{5EC1C3C0-6450-4D00-87A2-B1CAEF7C00D8}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Application", "Application", "{A01614A9-602D-49DE-A4EF-839F3E0CF013}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrellusElevate.Application", "src\Application\TrellusElevate.Application\TrellusElevate.Application.csproj", "{FE424C69-FA81-4B9C-A925-48D41A7F6040}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrellusElevate.Resources", "src\Application\TrellusElevate.Resources\TrellusElevate.Resources.csproj", "{5CA5E523-1E03-4E5E-95D2-B656AF3A31B5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Domain", "Domain", "{D16D47FB-7733-4959-8085-4026FF667840}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrellusElevate.Core", "src\Domain\TrellusElevate.Core\TrellusElevate.Core.csproj", "{323882E9-7BBB-4A53-94EE-EB49DCF371D5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{DED87149-E9CF-4E38-BCC6-EC4E0318CFDA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrellusElevate.Infrastructure", "src\Infrastructure\TrellusElevate.Infrastructure\TrellusElevate.Infrastructure.csproj", "{428BB7F0-B7B5-45B9-87B7-886B932680FB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentation", "Presentation", "{6ABB3119-ECE0-44DE-8E54-D8B2E82F0B50}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrellusElevate.Api", "src\Presentation\TrellusElevate.Api\TrellusElevate.Api.csproj", "{DAB96AF1-518A-4407-B3BD-7346A88FC1FF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrellusElevate.Presentation", "src\Presentation\TrellusElevate.Presentation\TrellusElevate.Presentation.csproj", "{9B3CD3FA-9699-4E72-ADA1-9792D75A9BEE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrellusElevate.Web", "src\Presentation\TrellusElevate.Web\TrellusElevate.Web.csproj", "{3A788A94-6079-476B-8DF1-612589C3D4A5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrellusElevate.IntegrationTests", "tests\TrellusElevate.IntegrationTests\TrellusElevate.IntegrationTests.csproj", "{38F96A48-5AB9-4CB4-997D-C088F901A456}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TrellusElevate.UnitTests", "tests\TrellusElevate.UnitTests\TrellusElevate.UnitTests.csproj", "{476FAB58-422C-4A93-A263-8F4383798B36}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FE424C69-FA81-4B9C-A925-48D41A7F6040}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE424C69-FA81-4B9C-A925-48D41A7F6040}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE424C69-FA81-4B9C-A925-48D41A7F6040}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE424C69-FA81-4B9C-A925-48D41A7F6040}.Release|Any CPU.Build.0 = Release|Any CPU
		{5CA5E523-1E03-4E5E-95D2-B656AF3A31B5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5CA5E523-1E03-4E5E-95D2-B656AF3A31B5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5CA5E523-1E03-4E5E-95D2-B656AF3A31B5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5CA5E523-1E03-4E5E-95D2-B656AF3A31B5}.Release|Any CPU.Build.0 = Release|Any CPU
		{323882E9-7BBB-4A53-94EE-EB49DCF371D5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{323882E9-7BBB-4A53-94EE-EB49DCF371D5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{323882E9-7BBB-4A53-94EE-EB49DCF371D5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{323882E9-7BBB-4A53-94EE-EB49DCF371D5}.Release|Any CPU.Build.0 = Release|Any CPU
		{428BB7F0-B7B5-45B9-87B7-886B932680FB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{428BB7F0-B7B5-45B9-87B7-886B932680FB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{428BB7F0-B7B5-45B9-87B7-886B932680FB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{428BB7F0-B7B5-45B9-87B7-886B932680FB}.Release|Any CPU.Build.0 = Release|Any CPU
		{DAB96AF1-518A-4407-B3BD-7346A88FC1FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DAB96AF1-518A-4407-B3BD-7346A88FC1FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DAB96AF1-518A-4407-B3BD-7346A88FC1FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DAB96AF1-518A-4407-B3BD-7346A88FC1FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{9B3CD3FA-9699-4E72-ADA1-9792D75A9BEE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B3CD3FA-9699-4E72-ADA1-9792D75A9BEE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B3CD3FA-9699-4E72-ADA1-9792D75A9BEE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B3CD3FA-9699-4E72-ADA1-9792D75A9BEE}.Release|Any CPU.Build.0 = Release|Any CPU
		{3A788A94-6079-476B-8DF1-612589C3D4A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3A788A94-6079-476B-8DF1-612589C3D4A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3A788A94-6079-476B-8DF1-612589C3D4A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3A788A94-6079-476B-8DF1-612589C3D4A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{38F96A48-5AB9-4CB4-997D-C088F901A456}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38F96A48-5AB9-4CB4-997D-C088F901A456}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38F96A48-5AB9-4CB4-997D-C088F901A456}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38F96A48-5AB9-4CB4-997D-C088F901A456}.Release|Any CPU.Build.0 = Release|Any CPU
		{476FAB58-422C-4A93-A263-8F4383798B36}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{476FAB58-422C-4A93-A263-8F4383798B36}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{476FAB58-422C-4A93-A263-8F4383798B36}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{476FAB58-422C-4A93-A263-8F4383798B36}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A01614A9-602D-49DE-A4EF-839F3E0CF013} = {FCCF4B6C-71BA-441C-A4D6-15D734B3F188}
		{FE424C69-FA81-4B9C-A925-48D41A7F6040} = {A01614A9-602D-49DE-A4EF-839F3E0CF013}
		{5CA5E523-1E03-4E5E-95D2-B656AF3A31B5} = {A01614A9-602D-49DE-A4EF-839F3E0CF013}
		{D16D47FB-7733-4959-8085-4026FF667840} = {FCCF4B6C-71BA-441C-A4D6-15D734B3F188}
		{323882E9-7BBB-4A53-94EE-EB49DCF371D5} = {D16D47FB-7733-4959-8085-4026FF667840}
		{DED87149-E9CF-4E38-BCC6-EC4E0318CFDA} = {FCCF4B6C-71BA-441C-A4D6-15D734B3F188}
		{428BB7F0-B7B5-45B9-87B7-886B932680FB} = {DED87149-E9CF-4E38-BCC6-EC4E0318CFDA}
		{6ABB3119-ECE0-44DE-8E54-D8B2E82F0B50} = {FCCF4B6C-71BA-441C-A4D6-15D734B3F188}
		{DAB96AF1-518A-4407-B3BD-7346A88FC1FF} = {6ABB3119-ECE0-44DE-8E54-D8B2E82F0B50}
		{9B3CD3FA-9699-4E72-ADA1-9792D75A9BEE} = {6ABB3119-ECE0-44DE-8E54-D8B2E82F0B50}
		{3A788A94-6079-476B-8DF1-612589C3D4A5} = {6ABB3119-ECE0-44DE-8E54-D8B2E82F0B50}
		{38F96A48-5AB9-4CB4-997D-C088F901A456} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
		{476FAB58-422C-4A93-A263-8F4383798B36} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
EndGlobal
