using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;
using TrellusElevate.Application.Interfaces.Data;
using TrellusElevate.Infrastructure.Data;
using TrellusElevate.Infrastructure.Data.Interceptors;
using TrellusElevate.Infrastructure.Data.Repositories;

namespace TrellusElevate.Infrastructure;

public static class Configuration
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<ISaveChangesInterceptor, AuditableEntityInterceptor>();
        services.AddSingleton<ISaveChangesInterceptor, SoftDeleteInterceptor>();
        services.AddSingleton<ISaveChangesInterceptor, DomainEventsInterceptor>();

        var dataSource = new NpgsqlDataSourceBuilder(configuration.GetConnectionString("Postgres"))
            .EnableDynamicJson()
            .Build();

        services.AddDbContext<AppDbContext>((sp, opt) =>
            opt.UseNpgsql(dataSource)
                .UseSnakeCaseNamingConvention()
                .AddInterceptors(sp.GetServices<ISaveChangesInterceptor>())
        );
        
        services.AddScoped(typeof(IRepository<>), typeof(EfRepository<>));
        services.AddScoped(typeof(IReadRepository<>), typeof(EfRepository<>));
        
        return services;
    }
    
    public static IServiceCollection AddInfrastructureWeb(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<ISaveChangesInterceptor, AuditableEntityInterceptor>();
        services.AddSingleton<ISaveChangesInterceptor, SoftDeleteInterceptor>();
        services.AddSingleton<ISaveChangesInterceptor, DomainEventsInterceptor>();

        var dataSource = new NpgsqlDataSourceBuilder(configuration.GetConnectionString("Postgres"))
            .EnableDynamicJson()
            .Build();

        services.AddDbContextFactory<AppDbContext>((sp, opt) =>
            opt.UseNpgsql(dataSource)
                .UseSnakeCaseNamingConvention()
                .AddInterceptors(sp.GetServices<ISaveChangesInterceptor>()));
        
        services.AddScoped(typeof(IRepository<>), typeof(EfRepository<>));
        services.AddScoped(typeof(IReadRepository<>), typeof(EfRepository<>));
        
        return services;
    }
}