<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Application\TrellusElevate.Application\TrellusElevate.Application.csproj" />
      <ProjectReference Include="..\..\Domain\TrellusElevate.Core\TrellusElevate.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Ardalis.Specification.EntityFrameworkCore" Version="9.2.0" />
        <PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Data\Migrations\" />
    </ItemGroup>
</Project>
