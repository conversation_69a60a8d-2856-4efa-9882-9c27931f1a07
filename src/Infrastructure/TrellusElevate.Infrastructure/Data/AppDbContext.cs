using TrellusElevate.Core.Domains.AssessmentResponses.Entities;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Patients.Entities;
using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Infrastructure.Data;

public sealed class AppDbContext(DbContextOptions<AppDbContext> options) : DbContext(options)
{
    public DbSet<Questionnaire> Questionnaires { get; set; }
    public DbSet<QuestionnaireItem> QuestionnaireItems { get; set; }
    public DbSet<QuestionnaireResponse> QuestionnaireResponses { get; set; }
    public DbSet<QuestionnaireResponseItem> QuestionnaireResponseItems { get; set; }
    
    public DbSet<Assessment> Assessments { get; set; }
    public DbSet<AssessmentScoringProfile> AssessmentScoringProfiles { get; set; }
    public DbSet<AssessmentScoreMapping> AssessmentScoreMappings { get; set; }
    
    public DbSet<AssessmentResponse> AssessmentResponses { get; set; }
    public DbSet<AssessmentResponseScore> AssessmentResponseScores { get; set; }
    
    public DbSet<Patient> Patients { get; set; }
    public DbSet<PatientAssessment> PatientAssessments { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(System.Reflection.Assembly.GetExecutingAssembly());
        base.OnModelCreating(modelBuilder);
    }
}