// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;
using TrellusElevate.Infrastructure.Data;

#nullable disable

namespace TrellusElevate.Infrastructure.Data.Migrations
{
    [DbContext(typeof(AppDbContext))]
    partial class AppDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("TrellusElevate.Core.Domains.AssessmentResponses.Entities.AssessmentResponse", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("uuid")
                        .HasColumnName("assessment_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<Guid>("QuestionnaireResponseId")
                        .HasColumnType("uuid")
                        .HasColumnName("questionnaire_response_id");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_assessment_responses");

                    b.HasIndex("AssessmentId")
                        .HasDatabaseName("ix_assessment_responses_assessment_id");

                    b.HasIndex("QuestionnaireResponseId")
                        .HasDatabaseName("ix_assessment_responses_questionnaire_response_id");

                    b.ToTable("assessment_responses", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.AssessmentResponses.Entities.AssessmentResponseScore", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AssessmentResponseId")
                        .HasColumnType("uuid")
                        .HasColumnName("assessment_response_id");

                    b.Property<Guid>("AssessmentScoringProfileId")
                        .HasColumnType("uuid")
                        .HasColumnName("assessment_scoring_profile_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<double>("Value")
                        .HasColumnType("double precision")
                        .HasColumnName("value");

                    b.HasKey("Id")
                        .HasName("pk_assessment_response_scores");

                    b.HasIndex("AssessmentResponseId")
                        .HasDatabaseName("ix_assessment_response_scores_assessment_response_id");

                    b.HasIndex("AssessmentScoringProfileId")
                        .HasDatabaseName("ix_assessment_response_scores_assessment_scoring_profile_id");

                    b.ToTable("assessment_response_scores", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Assessments.Entities.Assessment", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("description");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<Guid>("QuestionnaireId")
                        .HasColumnType("uuid")
                        .HasColumnName("questionnaire_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_assessments");

                    b.HasIndex("QuestionnaireId")
                        .HasDatabaseName("ix_assessments_questionnaire_id");

                    b.ToTable("assessments", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Assessments.Entities.AssessmentScoreMapping", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<List<QuestionAnswer>>("Answers")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("answers");

                    b.Property<Guid>("AssessmentScoringProfileId")
                        .HasColumnType("uuid")
                        .HasColumnName("assessment_scoring_profile_id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<int>("Operator")
                        .HasColumnType("integer")
                        .HasColumnName("operator");

                    b.Property<Guid>("QuestionnaireItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("questionnaire_item_id");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.Property<double>("Weight")
                        .HasColumnType("double precision")
                        .HasColumnName("weight");

                    b.HasKey("Id")
                        .HasName("pk_assessment_score_mappings");

                    b.HasIndex("AssessmentScoringProfileId")
                        .HasDatabaseName("ix_assessment_score_mappings_assessment_scoring_profile_id");

                    b.HasIndex("QuestionnaireItemId")
                        .HasDatabaseName("ix_assessment_score_mappings_questionnaire_item_id");

                    b.ToTable("assessment_score_mappings", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Assessments.Entities.AssessmentScoringProfile", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("uuid")
                        .HasColumnName("assessment_id");

                    b.Property<string>("CalculationFormula")
                        .HasColumnType("text")
                        .HasColumnName("calculation_formula");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("description");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_assessment_scoring_profiles");

                    b.HasIndex("AssessmentId")
                        .HasDatabaseName("ix_assessment_scoring_profiles_assessment_id");

                    b.ToTable("assessment_scoring_profiles", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Patients.Entities.Patient", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateOnly>("BirthDate")
                        .HasColumnType("date")
                        .HasColumnName("birth_date");

                    b.Property<string>("ContactEmail")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("contact_email");

                    b.Property<string>("ContactPhone")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("contact_phone");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("first_name");

                    b.Property<int>("Gender")
                        .HasColumnType("integer")
                        .HasColumnName("gender");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("last_name");

                    b.Property<string>("MiddleName")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("middle_name");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_patients");

                    b.ToTable("patients", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Patients.Entities.PatientAssessment", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("AssessmentId")
                        .HasColumnType("uuid")
                        .HasColumnName("assessment_id");

                    b.Property<Guid?>("AssessmentResponseId")
                        .HasColumnType("uuid")
                        .HasColumnName("assessment_response_id");

                    b.Property<DateTimeOffset?>("CompletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("completed_at");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<Guid>("PatientId")
                        .HasColumnType("uuid")
                        .HasColumnName("patient_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_patient_assessments");

                    b.HasIndex("AssessmentId")
                        .HasDatabaseName("ix_patient_assessments_assessment_id");

                    b.HasIndex("AssessmentResponseId")
                        .HasDatabaseName("ix_patient_assessments_assessment_response_id");

                    b.HasIndex("PatientId")
                        .HasDatabaseName("ix_patient_assessments_patient_id");

                    b.ToTable("patient_assessments", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.QuestionnaireResponses.Entities.QuestionnaireResponse", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<Guid>("QuestionnaireId")
                        .HasColumnType("uuid")
                        .HasColumnName("questionnaire_id");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_questionnaire_responses");

                    b.ToTable("questionnaire_responses", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.QuestionnaireResponses.Entities.QuestionnaireResponseItem", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<List<QuestionAnswer>>("Answers")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("answers");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<Guid?>("ParentQuestionnaireResponseItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_questionnaire_response_item_id");

                    b.Property<Guid>("QuestionnaireItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("questionnaire_item_id");

                    b.Property<Guid>("QuestionnaireResponseId")
                        .HasColumnType("uuid")
                        .HasColumnName("questionnaire_response_id");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_questionnaire_response_items");

                    b.HasIndex("ParentQuestionnaireResponseItemId")
                        .HasDatabaseName("ix_questionnaire_response_items_parent_questionnaire_response_");

                    b.HasIndex("QuestionnaireItemId")
                        .HasDatabaseName("ix_questionnaire_response_items_questionnaire_item_id");

                    b.HasIndex("QuestionnaireResponseId")
                        .HasDatabaseName("ix_questionnaire_response_items_questionnaire_response_id");

                    b.ToTable("questionnaire_response_items", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Questionnaires.Entities.Questionnaire", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<Dictionary<string, object>>("Metadata")
                        .HasColumnType("jsonb")
                        .HasColumnName("metadata");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("name");

                    b.Property<int>("Status")
                        .HasColumnType("integer")
                        .HasColumnName("status");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("title");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_questionnaires");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("ix_questionnaires_name");

                    b.ToTable("questionnaires", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Questionnaires.Entities.QuestionnaireItem", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("created_by");

                    b.Property<int?>("DisabledDisplay")
                        .HasColumnType("integer")
                        .HasColumnName("disabled_display");

                    b.Property<int?>("EnableBehavior")
                        .HasColumnType("integer")
                        .HasColumnName("enable_behavior");

                    b.Property<List<EnableWhen>>("EnableWhen")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("enable_when");

                    b.Property<QuestionAnswer>("Initial")
                        .HasColumnType("jsonb")
                        .HasColumnName("initial");

                    b.Property<string>("LinkId")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)")
                        .HasColumnName("link_id");

                    b.Property<int?>("MaxLength")
                        .HasColumnType("integer")
                        .HasColumnName("max_length");

                    b.Property<Dictionary<string, object>>("Metadata")
                        .HasColumnType("jsonb")
                        .HasColumnName("metadata");

                    b.Property<List<AnswerOption>>("Options")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("options");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("integer")
                        .HasColumnName("order_number");

                    b.Property<Guid?>("ParentQuestionnaireItemId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_questionnaire_item_id");

                    b.Property<string>("Prefix")
                        .HasColumnType("text")
                        .HasColumnName("prefix");

                    b.Property<Guid>("QuestionnaireId")
                        .HasColumnType("uuid")
                        .HasColumnName("questionnaire_id");

                    b.Property<bool>("ReadOnly")
                        .HasColumnType("boolean")
                        .HasColumnName("read_only");

                    b.Property<bool>("Repeats")
                        .HasColumnType("boolean")
                        .HasColumnName("repeats");

                    b.Property<bool>("Required")
                        .HasColumnType("boolean")
                        .HasColumnName("required");

                    b.Property<string>("Text")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer")
                        .HasColumnName("type");

                    b.Property<int>("UiElementType")
                        .HasColumnType("integer")
                        .HasColumnName("ui_element_type");

                    b.Property<DateTimeOffset?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uuid")
                        .HasColumnName("updated_by");

                    b.HasKey("Id")
                        .HasName("pk_questionnaire_items");

                    b.HasIndex("ParentQuestionnaireItemId")
                        .HasDatabaseName("ix_questionnaire_items_parent_questionnaire_item_id");

                    b.HasIndex("QuestionnaireId", "LinkId")
                        .IsUnique()
                        .HasDatabaseName("ix_questionnaire_items_questionnaire_id_link_id");

                    b.ToTable("questionnaire_items", (string)null);
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.AssessmentResponses.Entities.AssessmentResponse", b =>
                {
                    b.HasOne("TrellusElevate.Core.Domains.Assessments.Entities.Assessment", "Assessment")
                        .WithMany()
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_assessment_responses_assessments_assessment_id");

                    b.HasOne("TrellusElevate.Core.Domains.QuestionnaireResponses.Entities.QuestionnaireResponse", "QuestionnaireResponse")
                        .WithMany()
                        .HasForeignKey("QuestionnaireResponseId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_assessment_responses_questionnaire_responses_questionnaire_");

                    b.Navigation("Assessment");

                    b.Navigation("QuestionnaireResponse");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.AssessmentResponses.Entities.AssessmentResponseScore", b =>
                {
                    b.HasOne("TrellusElevate.Core.Domains.AssessmentResponses.Entities.AssessmentResponse", null)
                        .WithMany("Scores")
                        .HasForeignKey("AssessmentResponseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_assessment_response_scores_assessment_responses_assessment_");

                    b.HasOne("TrellusElevate.Core.Domains.Assessments.Entities.AssessmentScoringProfile", "AssessmentScoringProfile")
                        .WithMany()
                        .HasForeignKey("AssessmentScoringProfileId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_assessment_response_scores_assessment_scoring_profiles_asse");

                    b.Navigation("AssessmentScoringProfile");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Assessments.Entities.Assessment", b =>
                {
                    b.HasOne("TrellusElevate.Core.Domains.Questionnaires.Entities.Questionnaire", "Questionnaire")
                        .WithMany()
                        .HasForeignKey("QuestionnaireId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_assessments_questionnaires_questionnaire_id");

                    b.Navigation("Questionnaire");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Assessments.Entities.AssessmentScoreMapping", b =>
                {
                    b.HasOne("TrellusElevate.Core.Domains.Assessments.Entities.AssessmentScoringProfile", null)
                        .WithMany("ScoreMappings")
                        .HasForeignKey("AssessmentScoringProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_assessment_score_mappings_assessment_scoring_profiles_asses");

                    b.HasOne("TrellusElevate.Core.Domains.Questionnaires.Entities.QuestionnaireItem", null)
                        .WithMany()
                        .HasForeignKey("QuestionnaireItemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_assessment_score_mappings_questionnaire_items_questionnaire");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Assessments.Entities.AssessmentScoringProfile", b =>
                {
                    b.HasOne("TrellusElevate.Core.Domains.Assessments.Entities.Assessment", null)
                        .WithMany("ScoringProfiles")
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_assessment_scoring_profiles_assessments_assessment_id");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Patients.Entities.PatientAssessment", b =>
                {
                    b.HasOne("TrellusElevate.Core.Domains.Assessments.Entities.Assessment", "Assessment")
                        .WithMany()
                        .HasForeignKey("AssessmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_patient_assessments_assessments_assessment_id");

                    b.HasOne("TrellusElevate.Core.Domains.AssessmentResponses.Entities.AssessmentResponse", "AssessmentResponse")
                        .WithMany()
                        .HasForeignKey("AssessmentResponseId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_patient_assessments_assessment_responses_assessment_respons");

                    b.HasOne("TrellusElevate.Core.Domains.Patients.Entities.Patient", null)
                        .WithMany("Assessments")
                        .HasForeignKey("PatientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_patient_assessments_patients_patient_id");

                    b.Navigation("Assessment");

                    b.Navigation("AssessmentResponse");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.QuestionnaireResponses.Entities.QuestionnaireResponseItem", b =>
                {
                    b.HasOne("TrellusElevate.Core.Domains.QuestionnaireResponses.Entities.QuestionnaireResponseItem", "ParentQuestionnaireResponseItem")
                        .WithMany("Items")
                        .HasForeignKey("ParentQuestionnaireResponseItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_questionnaire_response_items_questionnaire_response_items_p");

                    b.HasOne("TrellusElevate.Core.Domains.Questionnaires.Entities.QuestionnaireItem", "QuestionnaireItem")
                        .WithMany()
                        .HasForeignKey("QuestionnaireItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_questionnaire_response_items_questionnaire_items_questionna");

                    b.HasOne("TrellusElevate.Core.Domains.QuestionnaireResponses.Entities.QuestionnaireResponse", null)
                        .WithMany("Items")
                        .HasForeignKey("QuestionnaireResponseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_questionnaire_response_items_questionnaire_responses_questi");

                    b.Navigation("ParentQuestionnaireResponseItem");

                    b.Navigation("QuestionnaireItem");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Questionnaires.Entities.QuestionnaireItem", b =>
                {
                    b.HasOne("TrellusElevate.Core.Domains.Questionnaires.Entities.QuestionnaireItem", "ParentQuestionnaireItem")
                        .WithMany("Items")
                        .HasForeignKey("ParentQuestionnaireItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("fk_questionnaire_items_questionnaire_items_parent_questionnair");

                    b.HasOne("TrellusElevate.Core.Domains.Questionnaires.Entities.Questionnaire", null)
                        .WithMany("Items")
                        .HasForeignKey("QuestionnaireId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("fk_questionnaire_items_questionnaires_questionnaire_id");

                    b.Navigation("ParentQuestionnaireItem");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.AssessmentResponses.Entities.AssessmentResponse", b =>
                {
                    b.Navigation("Scores");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Assessments.Entities.Assessment", b =>
                {
                    b.Navigation("ScoringProfiles");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Assessments.Entities.AssessmentScoringProfile", b =>
                {
                    b.Navigation("ScoreMappings");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Patients.Entities.Patient", b =>
                {
                    b.Navigation("Assessments");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.QuestionnaireResponses.Entities.QuestionnaireResponse", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.QuestionnaireResponses.Entities.QuestionnaireResponseItem", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Questionnaires.Entities.Questionnaire", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("TrellusElevate.Core.Domains.Questionnaires.Entities.QuestionnaireItem", b =>
                {
                    b.Navigation("Items");
                });
#pragma warning restore 612, 618
        }
    }
}
