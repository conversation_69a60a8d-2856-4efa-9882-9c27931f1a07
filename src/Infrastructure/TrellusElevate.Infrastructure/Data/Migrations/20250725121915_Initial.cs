using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

#nullable disable

namespace TrellusElevate.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "patients",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    first_name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    middle_name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    last_name = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    birth_date = table.Column<DateOnly>(type: "date", nullable: false),
                    gender = table.Column<int>(type: "integer", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    contact_email = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    contact_phone = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_patients", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "questionnaire_responses",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    questionnaire_id = table.Column<Guid>(type: "uuid", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_questionnaire_responses", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "questionnaires",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    title = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    description = table.Column<string>(type: "text", nullable: true),
                    metadata = table.Column<Dictionary<string, object>>(type: "jsonb", nullable: true),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_questionnaires", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "assessments",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    questionnaire_id = table.Column<Guid>(type: "uuid", nullable: false),
                    status = table.Column<int>(type: "integer", nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_assessments", x => x.id);
                    table.ForeignKey(
                        name: "fk_assessments_questionnaires_questionnaire_id",
                        column: x => x.questionnaire_id,
                        principalTable: "questionnaires",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "questionnaire_items",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    questionnaire_id = table.Column<Guid>(type: "uuid", nullable: false),
                    parent_questionnaire_item_id = table.Column<Guid>(type: "uuid", nullable: true),
                    link_id = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    text = table.Column<string>(type: "text", nullable: false),
                    type = table.Column<int>(type: "integer", nullable: false),
                    order_number = table.Column<int>(type: "integer", nullable: false),
                    required = table.Column<bool>(type: "boolean", nullable: false),
                    enable_behavior = table.Column<int>(type: "integer", nullable: true),
                    disabled_display = table.Column<int>(type: "integer", nullable: true),
                    prefix = table.Column<string>(type: "text", nullable: true),
                    max_length = table.Column<int>(type: "integer", nullable: true),
                    read_only = table.Column<bool>(type: "boolean", nullable: false),
                    repeats = table.Column<bool>(type: "boolean", nullable: false),
                    initial = table.Column<QuestionAnswer>(type: "jsonb", nullable: true),
                    metadata = table.Column<Dictionary<string, object>>(type: "jsonb", nullable: true),
                    enable_when = table.Column<List<EnableWhen>>(type: "jsonb", nullable: false),
                    options = table.Column<List<AnswerOption>>(type: "jsonb", nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_questionnaire_items", x => x.id);
                    table.ForeignKey(
                        name: "fk_questionnaire_items_questionnaire_items_parent_questionnair",
                        column: x => x.parent_questionnaire_item_id,
                        principalTable: "questionnaire_items",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_questionnaire_items_questionnaires_questionnaire_id",
                        column: x => x.questionnaire_id,
                        principalTable: "questionnaires",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "assessment_responses",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    assessment_id = table.Column<Guid>(type: "uuid", nullable: false),
                    questionnaire_response_id = table.Column<Guid>(type: "uuid", nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_assessment_responses", x => x.id);
                    table.ForeignKey(
                        name: "fk_assessment_responses_assessments_assessment_id",
                        column: x => x.assessment_id,
                        principalTable: "assessments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_assessment_responses_questionnaire_responses_questionnaire_",
                        column: x => x.questionnaire_response_id,
                        principalTable: "questionnaire_responses",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "assessment_scoring_profiles",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    assessment_id = table.Column<Guid>(type: "uuid", nullable: false),
                    name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    calculation_formula = table.Column<string>(type: "text", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_assessment_scoring_profiles", x => x.id);
                    table.ForeignKey(
                        name: "fk_assessment_scoring_profiles_assessments_assessment_id",
                        column: x => x.assessment_id,
                        principalTable: "assessments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "questionnaire_response_items",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    questionnaire_response_id = table.Column<Guid>(type: "uuid", nullable: false),
                    parent_questionnaire_response_item_id = table.Column<Guid>(type: "uuid", nullable: true),
                    questionnaire_item_id = table.Column<Guid>(type: "uuid", nullable: false),
                    answers = table.Column<List<QuestionAnswer>>(type: "jsonb", nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_questionnaire_response_items", x => x.id);
                    table.ForeignKey(
                        name: "fk_questionnaire_response_items_questionnaire_items_questionna",
                        column: x => x.questionnaire_item_id,
                        principalTable: "questionnaire_items",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_questionnaire_response_items_questionnaire_response_items_p",
                        column: x => x.parent_questionnaire_response_item_id,
                        principalTable: "questionnaire_response_items",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_questionnaire_response_items_questionnaire_responses_questi",
                        column: x => x.questionnaire_response_id,
                        principalTable: "questionnaire_responses",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "patient_assessments",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    patient_id = table.Column<Guid>(type: "uuid", nullable: false),
                    assessment_id = table.Column<Guid>(type: "uuid", nullable: false),
                    assessment_response_id = table.Column<Guid>(type: "uuid", nullable: true),
                    status = table.Column<int>(type: "integer", nullable: false),
                    completed_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_patient_assessments", x => x.id);
                    table.ForeignKey(
                        name: "fk_patient_assessments_assessment_responses_assessment_respons",
                        column: x => x.assessment_response_id,
                        principalTable: "assessment_responses",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_patient_assessments_assessments_assessment_id",
                        column: x => x.assessment_id,
                        principalTable: "assessments",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_patient_assessments_patients_patient_id",
                        column: x => x.patient_id,
                        principalTable: "patients",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "assessment_response_scores",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    assessment_response_id = table.Column<Guid>(type: "uuid", nullable: false),
                    assessment_scoring_profile_id = table.Column<Guid>(type: "uuid", nullable: false),
                    value = table.Column<double>(type: "double precision", nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_assessment_response_scores", x => x.id);
                    table.ForeignKey(
                        name: "fk_assessment_response_scores_assessment_responses_assessment_",
                        column: x => x.assessment_response_id,
                        principalTable: "assessment_responses",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_assessment_response_scores_assessment_scoring_profiles_asse",
                        column: x => x.assessment_scoring_profile_id,
                        principalTable: "assessment_scoring_profiles",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "assessment_score_mappings",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    assessment_scoring_profile_id = table.Column<Guid>(type: "uuid", nullable: false),
                    questionnaire_item_id = table.Column<Guid>(type: "uuid", nullable: false),
                    answer = table.Column<QuestionAnswer>(type: "jsonb", nullable: false),
                    @operator = table.Column<int>(name: "operator", type: "integer", nullable: false),
                    weight = table.Column<double>(type: "double precision", nullable: false),
                    created_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false),
                    updated_at = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    created_by = table.Column<Guid>(type: "uuid", nullable: true),
                    updated_by = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_assessment_score_mappings", x => x.id);
                    table.ForeignKey(
                        name: "fk_assessment_score_mappings_assessment_scoring_profiles_asses",
                        column: x => x.assessment_scoring_profile_id,
                        principalTable: "assessment_scoring_profiles",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_assessment_score_mappings_questionnaire_items_questionnaire",
                        column: x => x.questionnaire_item_id,
                        principalTable: "questionnaire_items",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "ix_assessment_response_scores_assessment_response_id",
                table: "assessment_response_scores",
                column: "assessment_response_id");

            migrationBuilder.CreateIndex(
                name: "ix_assessment_response_scores_assessment_scoring_profile_id",
                table: "assessment_response_scores",
                column: "assessment_scoring_profile_id");

            migrationBuilder.CreateIndex(
                name: "ix_assessment_responses_assessment_id",
                table: "assessment_responses",
                column: "assessment_id");

            migrationBuilder.CreateIndex(
                name: "ix_assessment_responses_questionnaire_response_id",
                table: "assessment_responses",
                column: "questionnaire_response_id");

            migrationBuilder.CreateIndex(
                name: "ix_assessment_score_mappings_assessment_scoring_profile_id",
                table: "assessment_score_mappings",
                column: "assessment_scoring_profile_id");

            migrationBuilder.CreateIndex(
                name: "ix_assessment_score_mappings_questionnaire_item_id",
                table: "assessment_score_mappings",
                column: "questionnaire_item_id");

            migrationBuilder.CreateIndex(
                name: "ix_assessment_scoring_profiles_assessment_id",
                table: "assessment_scoring_profiles",
                column: "assessment_id");

            migrationBuilder.CreateIndex(
                name: "ix_assessments_questionnaire_id",
                table: "assessments",
                column: "questionnaire_id");

            migrationBuilder.CreateIndex(
                name: "ix_patient_assessments_assessment_id",
                table: "patient_assessments",
                column: "assessment_id");

            migrationBuilder.CreateIndex(
                name: "ix_patient_assessments_assessment_response_id",
                table: "patient_assessments",
                column: "assessment_response_id");

            migrationBuilder.CreateIndex(
                name: "ix_patient_assessments_patient_id",
                table: "patient_assessments",
                column: "patient_id");

            migrationBuilder.CreateIndex(
                name: "ix_questionnaire_items_parent_questionnaire_item_id",
                table: "questionnaire_items",
                column: "parent_questionnaire_item_id");

            migrationBuilder.CreateIndex(
                name: "ix_questionnaire_items_questionnaire_id_link_id",
                table: "questionnaire_items",
                columns: new[] { "questionnaire_id", "link_id" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_questionnaire_response_items_parent_questionnaire_response_",
                table: "questionnaire_response_items",
                column: "parent_questionnaire_response_item_id");

            migrationBuilder.CreateIndex(
                name: "ix_questionnaire_response_items_questionnaire_item_id",
                table: "questionnaire_response_items",
                column: "questionnaire_item_id");

            migrationBuilder.CreateIndex(
                name: "ix_questionnaire_response_items_questionnaire_response_id",
                table: "questionnaire_response_items",
                column: "questionnaire_response_id");

            migrationBuilder.CreateIndex(
                name: "ix_questionnaires_name",
                table: "questionnaires",
                column: "name",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "assessment_response_scores");

            migrationBuilder.DropTable(
                name: "assessment_score_mappings");

            migrationBuilder.DropTable(
                name: "patient_assessments");

            migrationBuilder.DropTable(
                name: "questionnaire_response_items");

            migrationBuilder.DropTable(
                name: "assessment_scoring_profiles");

            migrationBuilder.DropTable(
                name: "assessment_responses");

            migrationBuilder.DropTable(
                name: "patients");

            migrationBuilder.DropTable(
                name: "questionnaire_items");

            migrationBuilder.DropTable(
                name: "assessments");

            migrationBuilder.DropTable(
                name: "questionnaire_responses");

            migrationBuilder.DropTable(
                name: "questionnaires");
        }
    }
}
