using Ardalis.Specification.EntityFrameworkCore;
using TrellusElevate.Application.Interfaces.Data;
using TrellusElevate.Core.Common;

namespace TrellusElevate.Infrastructure.Data.Repositories;

public class EfRepository<T> : RepositoryBase<T>, IReadRepository<T>, IRepository<T> where T : class, IAggregateRoot
{
    private readonly AppDbContext _dbContext;

    public EfRepository(AppDbContext dbContext) : base(dbContext)
    {
        _dbContext = dbContext;
    }
    
    public async Task<T> AddAsync(T entity, bool saveChanges = true, CancellationToken cancellationToken = default)
    {
        _dbContext.Set<T>().Add(entity);

        if (saveChanges)
        {
            await SaveChangesAsync(cancellationToken);
        }

        return entity;
    }
    
    public async Task<T> UpdateAsync(T entity, bool saveChanges = true, CancellationToken cancellationToken = default)
    {
        _dbContext.Set<T>().Update(entity);

        if (saveChanges)
        {
            await SaveChangesAsync(cancellationToken);
        }

        return entity;
    }
}
