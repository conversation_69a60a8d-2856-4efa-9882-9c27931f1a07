using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.QuestionnaireResponses;

internal class QuestionnaireResponseItemConfiguration : IEntityTypeConfiguration<QuestionnaireResponseItem>
{
    public void Configure(EntityTypeBuilder<QuestionnaireResponseItem> builder)
    {
        builder.Property(s => s.Id).ValueGeneratedNever();

        builder.Navigation(p => p.QuestionnaireItem).AutoInclude();

        builder.HasOne(q => q.ParentQuestionnaireResponseItem)
            .WithMany(q => q.Items)
            .HasForeignKey(q => q.ParentQuestionnaireResponseItemId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Property(p => p.Answers)
            .IsRequired()
            .HasColumnType("jsonb");
    }
}