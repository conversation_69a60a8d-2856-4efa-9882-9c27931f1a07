using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.QuestionnaireResponses;

internal sealed class QuestionnaireResponseConfiguration : IEntityTypeConfiguration<QuestionnaireResponse>
{
    public void Configure(EntityTypeBuilder<QuestionnaireResponse> builder)
    {
        builder.Property(s => s.Id).ValueGeneratedNever();
        builder.Property(p => p.Status).IsRequired();
        
        builder.Navigation(p => p.Items).AutoInclude();
        builder.HasMany(p => p.Items)
            .WithOne()
            .HasForeignKey(p => p.QuestionnaireResponseId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}