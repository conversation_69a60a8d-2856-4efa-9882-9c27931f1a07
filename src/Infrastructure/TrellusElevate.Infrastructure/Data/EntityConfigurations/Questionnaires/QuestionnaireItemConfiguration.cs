using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.Questionnaires;

internal class QuestionnaireItemConfiguration : IEntityTypeConfiguration<QuestionnaireItem>
{
    public void Configure(EntityTypeBuilder<QuestionnaireItem> builder)
    {
        builder.Property(s => s.Id).ValueGeneratedNever();
        builder.Property(p => p.LinkId)
            .HasMaxLength(255)
            .IsRequired();
        builder.HasIndex(p => new { p.QuestionnaireId, p.LinkId }).IsUnique();
        builder.Property(p => p.Text).IsRequired();
        builder.Property(p => p.Type).IsRequired();
        builder.Property(p => p.EnableWhen).HasColumnType("jsonb");
        builder.Property(p => p.Options).HasColumnType("jsonb");
        builder.Property(p => p.Initial).HasColumnType("jsonb");
        builder.Property(p => p.Metadata).HasColumnType("jsonb");

        builder.HasOne(q => q.ParentQuestionnaireItem)
            .WithMany(q => q.Items)
            .HasForeignKey(q => q.ParentQuestionnaireItemId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}