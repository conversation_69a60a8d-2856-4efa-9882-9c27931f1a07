using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.Questionnaires;

internal sealed class QuestionnaireConfiguration : IEntityTypeConfiguration<Questionnaire>
{
    public void Configure(EntityTypeBuilder<Questionnaire> builder)
    {
        builder.Property(s => s.Id).ValueGeneratedNever();
        builder.Property(p => p.Name).HasMaxLength(100).IsRequired();
        builder.HasIndex(p => p.Name).IsUnique();
        builder.Property(p => p.Title).HasMaxLength(100).IsRequired();
        builder.Property(p => p.Metadata).HasColumnType("jsonb");

        builder.Navigation(p => p.Items).AutoInclude();
        builder.HasMany(p => p.Items)
            .WithOne()
            .HasForeignKey(p => p.QuestionnaireId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}