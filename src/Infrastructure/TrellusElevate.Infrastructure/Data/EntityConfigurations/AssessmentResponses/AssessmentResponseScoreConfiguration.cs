using TrellusElevate.Core.Domains.AssessmentResponses.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.AssessmentResponses;

internal class AssessmentResponseScoreConfiguration : IEntityTypeConfiguration<AssessmentResponseScore>
{
    public void Configure(EntityTypeBuilder<AssessmentResponseScore> builder)
    {
        builder.Property(s => s.Id).ValueGeneratedNever();

        builder.HasOne(p => p.AssessmentScoringProfile)
            .WithMany()
            .HasForeignKey(e => e.AssessmentScoringProfileId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}