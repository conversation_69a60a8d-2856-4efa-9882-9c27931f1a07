using TrellusElevate.Core.Domains.AssessmentResponses.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.AssessmentResponses;

internal class AssessmentResponseConfiguration : IEntityTypeConfiguration<AssessmentResponse>
{
    public void Configure(EntityTypeBuilder<AssessmentResponse> builder)
    {
        builder.Property(s => s.Id).ValueGeneratedNever();
        
        builder.HasOne(p => p.Assessment)
            .WithMany()
            .HasForeignKey(e => e.AssessmentId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(p => p.QuestionnaireResponse)
            .WithMany()
            .HasForeignKey(e => e.QuestionnaireResponseId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(e => e.Scores)
            .WithOne()
            .HasForeignKey(e => e.AssessmentResponseId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}