using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.Patients;

internal sealed class PatientAssessmentConfiguration : IEntityTypeConfiguration<PatientAssessment>
{
    public void Configure(EntityTypeBuilder<PatientAssessment> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        
        builder.Property(p => p.Status).IsRequired();

        builder.HasOne(p => p.Assessment)
            .WithMany()
            .HasForeignKey(e => e.AssessmentId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.HasOne(p => p.AssessmentResponse)
            .WithMany()
            .HasForeignKey(e => e.AssessmentResponseId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}