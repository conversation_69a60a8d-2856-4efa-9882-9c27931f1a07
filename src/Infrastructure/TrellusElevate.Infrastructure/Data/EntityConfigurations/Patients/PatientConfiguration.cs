using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.Patients;

internal sealed class PatientConfiguration : IEntityTypeConfiguration<Patient>
{
    public void Configure(EntityTypeBuilder<Patient> builder)
    {
        builder.Property(p => p.Id).ValueGeneratedNever();
        builder.Property(p => p.FirstName).HasMaxLength(50).IsRequired();
        builder.Property(p => p.MiddleName).HasMaxLength(50);
        builder.Property(p => p.LastName).HasMaxLength(50).IsRequired();
        builder.Property(p => p.BirthDate).IsRequired();
        builder.Property(p => p.Gender).IsRequired();
        builder.Property(p => p.Status).IsRequired();
        builder.Property(p => p.ContactEmail).HasMaxLength(100).IsRequired();
        builder.Property(p => p.ContactPhone).HasMaxLength(100).IsRequired();
        
        builder.HasMany(e => e.Assessments)
            .WithOne()
            .HasForeignKey(e => e.PatientId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}