using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.Assessments;

public class AssessmentScoringProfileConfiguration : IEntityTypeConfiguration<AssessmentScoringProfile>
{
    public void Configure(EntityTypeBuilder<AssessmentScoringProfile> builder)
    {
        builder.Property(s => s.Id).ValueGeneratedNever();
        
        builder.Property(e => e.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasMaxLength(1000);

        builder.Property(e => e.Status)
            .IsRequired();

        builder.HasMany(e => e.ScoreMappings)
            .WithOne()
            .HasForeignKey(e => e.AssessmentScoringProfileId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}