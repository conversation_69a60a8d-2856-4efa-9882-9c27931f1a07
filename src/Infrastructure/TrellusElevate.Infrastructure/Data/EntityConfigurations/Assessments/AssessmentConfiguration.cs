using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.Assessments;

public class AssessmentConfiguration : IEntityTypeConfiguration<Assessment>
{
    public void Configure(EntityTypeBuilder<Assessment> builder)
    {
        builder.Property(s => s.Id).ValueGeneratedNever();
        
        builder.Property(e => e.Name)
            .HasMaxLength(100)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasMaxLength(1000);

        builder.Property(e => e.Status)
            .IsRequired();

        builder.HasMany(e => e.ScoringProfiles)
            .WithOne()
            .HasForeignKey(e => e.AssessmentId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(p => p.Questionnaire)
            .WithMany()
            .HasForeignKey(e => e.QuestionnaireId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}