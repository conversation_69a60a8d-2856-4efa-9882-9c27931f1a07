using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Infrastructure.Data.EntityConfigurations.Assessments;

public class AssessmentScoreMappingConfiguration : IEntityTypeConfiguration<AssessmentScoreMapping>
{
    public void Configure(EntityTypeBuilder<AssessmentScoreMapping> builder)
    {
        builder.Property(s => s.Id).ValueGeneratedNever();

        builder.Property(p => p.Answers).HasColumnType("jsonb").IsRequired();

        builder.Property(e => e.Operator).IsRequired();

        builder.Property(e => e.Weight).IsRequired();

        builder.HasOne<QuestionnaireItem>()
            .WithMany()
            .HasForeignKey(e => e.QuestionnaireItemId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}