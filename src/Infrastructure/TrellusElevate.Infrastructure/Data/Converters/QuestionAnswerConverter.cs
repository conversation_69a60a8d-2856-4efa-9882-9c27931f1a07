using System.Text.Json;
using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Infrastructure.Data.Converters;

public sealed class QuestionAnswerConverter() : ValueConverter<QuestionAnswer, string>(
    v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
    v => DeserializeQuestionAnswer(v))
{
    private static QuestionAnswer DeserializeQuestionAnswer(string json)
    {
        try
        {
            var element = JsonSerializer.Deserialize<JsonDocument>(json)!.RootElement;
            
            // Extract the Type property
            var typeProperty = element.GetProperty("Type");
            var answerType = (AnswerType)typeProperty.GetInt32();
            
            // Extract the Value property
            var valueProperty = element.GetProperty("Value");
            
            // Convert the value based on the question type
            object? convertedValue;
            switch (answerType)
            {
                case AnswerType.Boolean:
                    convertedValue = valueProperty.ValueKind == JsonValueKind.True;
                    break;
                case AnswerType.Decimal:
                    convertedValue = valueProperty.TryGetDecimal(out var d) ? d : 0m;
                    break;
                case AnswerType.Integer:
                    convertedValue = valueProperty.TryGetInt32(out var i) ? i : 0;
                    break;
                case AnswerType.Date:
                    convertedValue = valueProperty.TryGetDateTime(out var dateTime) ? DateOnly.FromDateTime(dateTime) : DateOnly.MinValue;
                    break;
                case AnswerType.DateTime:
                    convertedValue = valueProperty.TryGetDateTime(out var dt) ? dt : DateTime.MinValue;
                    break;
                case AnswerType.Time:
                    convertedValue = valueProperty.TryGetDateTime(out var t) ?
                        TimeOnly.FromDateTime(t) : TimeOnly.MinValue;
                    break;
                case AnswerType.String:
                case AnswerType.Text:
                case AnswerType.Url:
                    convertedValue = valueProperty.GetString() ?? string.Empty;
                    break;
                default:
                    convertedValue = null;
                    break;
            }
            
            return new QuestionAnswer(convertedValue, answerType);
        }
        catch
        {
            // Return a default value or throw an exception based on your requirements
            return new QuestionAnswer();
        }
    }
}