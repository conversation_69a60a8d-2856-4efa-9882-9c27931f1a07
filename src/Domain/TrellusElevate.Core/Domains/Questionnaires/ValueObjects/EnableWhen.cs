using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

public sealed class EnableWhen : ValueObject
{
    public string? LinkId { get; set; }
    public Operator Operator { get; set; }
    public QuestionAnswer? Answer { get; set; }

    public EnableWhen(string? linkId, Operator @operator, QuestionAnswer? answer)
    {
        LinkId = linkId;
        Operator = @operator;
        Answer = answer;
    }

    public EnableWhen()
    {
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return LinkId!;
        yield return Operator;
        yield return Answer!;
    }
}