namespace TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

public sealed class AnswerOption : ValueObject
{
    public QuestionAnswer? Answer { get; set; }
    public string? Title { get; set; }
    public bool InitialSelected { get; set; }

    public AnswerOption(QuestionAnswer answer, string? title, bool initialSelected)
    {
        Answer = answer;
        Title = title;
        InitialSelected = initialSelected;
    }

    public AnswerOption()
    {
        
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Answer;
        yield return InitialSelected;
        yield return Title!;
    }
}