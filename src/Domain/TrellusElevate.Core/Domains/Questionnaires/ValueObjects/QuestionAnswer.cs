using System.Text.Json.Serialization;
using TrellusElevate.Core.Domains.Questionnaires.Common;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

[JsonConverter(typeof(QuestionAnswerJsonConverter))]
public sealed class QuestionAnswer : ValueObject
{
    public object? Value { get; set; }
    public AnswerType? Type { get; set; }

    public QuestionAnswer(object? value, AnswerType? type)
    {
        Value = value;
        Type = type;
    }

    public QuestionAnswer()
    {
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Value;
        yield return Type;
    }

    /// <summary>
    /// Gets the value as the specified type T. Returns default(T) if the value is null or cannot be converted.
    /// </summary>
    /// <typeparam name="T">The type to convert the value to</typeparam>
    /// <returns>The value as type T, or default(T) if conversion fails</returns>
    public T? GetValue<T>()
    {
        if (Value == null)
            return default;

        try
        {
            // Handle nullable types
            var targetType = typeof(T);
            var underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

            // Direct type match
            if (Value.GetType() == underlyingType || Value.GetType() == targetType)
                return (T)Value;

            // Handle string conversions
            if (underlyingType == typeof(string))
                return (T)(object)Value.ToString()!;

            // Handle conversions using Convert.ChangeType
            if (underlyingType.IsPrimitive || underlyingType == typeof(decimal) || underlyingType == typeof(DateTime) || underlyingType == typeof(DateOnly) || underlyingType == typeof(TimeOnly))
            {
                var convertedValue = Convert.ChangeType(Value, underlyingType);
                return (T)convertedValue;
            }

            // Fallback: try direct cast
            return (T)Value;
        }
        catch
        {
            return default;
        }
    }

    
    /// <summary>
    /// Checks if a list of answers satisfies the mapping answer based on the specified operator.
    /// This method is specifically designed for multiselect questions.
    /// </summary>
    /// <param name="responseAnswers">The list of answers from a questionnaire response.</param>
    /// <param name="mappingAnswers">The list of answers from a score mapping.</param>
    /// <param name="op">The comparison operator.</param>
    /// <returns>True if the comparison condition is met, false otherwise.</returns>
    public static bool IsSatisfiedBy(List<QuestionAnswer> responseAnswers, List<QuestionAnswer> mappingAnswers, Operator op)
    {
        if (responseAnswers.Count == 0)
        {
            if (mappingAnswers.Count == 0)
            {
                return op is Operator.Equals or Operator.Exists;
            }
            return op != Operator.Exists && op is Operator.NotEquals or Operator.NotContains;
        }

        if (mappingAnswers.Count == 0)
        {
            return op == Operator.Exists && responseAnswers.Count > 0;
        }

        try
        {
            return op switch
            {
                Operator.Equals => AreListsEqual(responseAnswers, mappingAnswers),
                Operator.NotEquals => !AreListsEqual(responseAnswers, mappingAnswers),
                Operator.Exists => responseAnswers.Count > 0,
                Operator.Contains => mappingAnswers.Any(mappingAnswer => responseAnswers.Any(responseAnswer => responseAnswer.Equals(mappingAnswer))),
                Operator.NotContains => !mappingAnswers.Any(mappingAnswer => responseAnswers.Any(responseAnswer => responseAnswer.Equals(mappingAnswer))),
                // For comparison operators on multiselect, we compare against the first mapping answer
                Operator.GreaterThan => responseAnswers.Any(ra => mappingAnswers.Any(ma => CompareValues(ra.Value!, ma.Value!, ra.Type) > 0)),
                Operator.GreaterThanOrEquals => responseAnswers.Any(ra => mappingAnswers.Any(ma => CompareValues(ra.Value!, ma.Value!, ra.Type) >= 0)),
                Operator.LessThan => responseAnswers.Any(ra => mappingAnswers.Any(ma => CompareValues(ra.Value!, ma.Value!, ra.Type) < 0)),
                Operator.LessThanOrEquals => responseAnswers.Any(ra => mappingAnswers.Any(ma => CompareValues(ra.Value!, ma.Value!, ra.Type) <= 0)),
                _ => false
            };
        }
        catch (Exception)
        {
            return false;
        }
    }

    private static bool AreListsEqual(List<QuestionAnswer> list1, List<QuestionAnswer> list2)
    {
        if (list1.Count != list2.Count) return false;

        // Sort both lists for comparison (by value and type)
        var sorted1 = list1.OrderBy(x => x.Value?.ToString()).ThenBy(x => x.Type).ToList();
        var sorted2 = list2.OrderBy(x => x.Value?.ToString()).ThenBy(x => x.Type).ToList();

        return sorted1.SequenceEqual(sorted2);
    }

    /// <summary>
    /// Helper method to perform type-specific comparisons for relational operators.
    /// </summary>
    /// <param name="val1">The first value to compare.</param>
    /// <param name="val2">The second value to compare.</param>
    /// <param name="type">The expected AnswerType for comparison.</param>
    /// <returns>A signed integer that indicates the relative values of val1 and val2.</returns>
    /// <exception cref="InvalidOperationException">Thrown if the AnswerType is not supported for comparison.</exception>
    private static int CompareValues(object val1, object val2, AnswerType? type)
    {
        switch (type)
        {
            case AnswerType.Boolean:
                return ((bool)val1).CompareTo((bool)val2);
            case AnswerType.Decimal:
                return ((decimal)val1).CompareTo((decimal)val2);
            case AnswerType.Integer:
                return ((int)val1).CompareTo((int)val2);
            case AnswerType.Date:
                return ((DateOnly)val1).CompareTo((DateOnly)val2);
            case AnswerType.DateTime:
                return ((DateTime)val1).CompareTo((DateTime)val2);
            case AnswerType.Time:
                return ((TimeOnly)val1).CompareTo((TimeOnly)val2);
            case AnswerType.String:
            case AnswerType.Text:
            case AnswerType.Url:
                return string.CompareOrdinal(val1.ToString(), val2.ToString());
            default:
                throw new InvalidOperationException($"Comparison not supported for AnswerType: {type}");
        }
    }
}