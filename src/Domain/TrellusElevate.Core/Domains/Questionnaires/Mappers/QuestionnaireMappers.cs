using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Core.Domains.Questionnaires.Mappers;

public static class QuestionnaireMappers
{
    public static AnswerType? ToAnswerType(this QuestionType? questionType) =>
        questionType switch
        {
            null => null,   
            QuestionType.Boolean => AnswerType.Boolean,
            QuestionType.Date => AnswerType.Date,
            QuestionType.DateTime => AnswerType.DateTime,
            QuestionType.Decimal => AnswerType.Decimal,
            QuestionType.Integer => AnswerType.Integer,
            QuestionType.String => AnswerType.String,
            QuestionType.Text => AnswerType.Text,
            QuestionType.Time => AnswerType.Time,
            QuestionType.Url => AnswerType.Url,
            _ => throw new NotSupportedException("Not supported conversion")
        };
    
    public static AnswerType ToAnswerType(this QuestionType questionType) =>
        questionType switch
        {
            QuestionType.Boolean => AnswerType.Boolean,
            QuestionType.Date => AnswerType.Date,
            QuestionType.DateTime => AnswerType.DateTime,
            QuestionType.Decimal => AnswerType.Decimal,
            QuestionType.Integer => AnswerType.Integer,
            QuestionType.String => AnswerType.String,
            QuestionType.Text => AnswerType.Text,
            QuestionType.Time => AnswerType.Time,
            QuestionType.Url => AnswerType.Url,
            _ => throw new NotSupportedException("Not supported conversion")
        };
}