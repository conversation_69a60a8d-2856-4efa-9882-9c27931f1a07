using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Core.Domains.Questionnaires.Entities;

public sealed class Questionnaire : EntityBase<Guid>, IAggregateRoot
{
    public string Name { get; private set; } = null!;
    public string Title { get; private set; } = null!;
    public QuestionnaireStatus Status { get; private set; }
    public string? Description { get; private set; }
    public Dictionary<string, object>? Metadata { get; private set; }

    private readonly List<QuestionnaireItem> _items = [];
    public IReadOnlyCollection<QuestionnaireItem> Items => _items.AsReadOnly();

    private Questionnaire()
    {
    }

    public static Result<Questionnaire> Create(
        string name,
        string title,
        string? description,
        QuestionnaireStatus status,
        Dictionary<string, object>? metadata
    )
    {
        if (string.IsNullOrWhiteSpace(name)) return QuestionnaireErrors.InvalidName(name);
        if (string.IsNullOrWhiteSpace(title)) return QuestionnaireErrors.InvalidTitle(title);

        return new Questionnaire
        {
            Id = Guid.CreateVersion7(),
            Name = name,
            Title = title,
            Status = status,
            Description = description,
            Metadata = metadata ?? new Dictionary<string, object>()
        };
    }

    public Result Update(
        string name,
        string title,
        string? description,
        QuestionnaireStatus status,
        Dictionary<string, object>? metadata
    )
    {
        if (string.IsNullOrWhiteSpace(name)) return QuestionnaireErrors.InvalidName(name);
        if (string.IsNullOrWhiteSpace(title)) return QuestionnaireErrors.InvalidTitle(title);

        Name = name;
        Title = title;
        Status = status;
        Description = description;
        Metadata = metadata ?? new Dictionary<string, object>();

        return Result.Ok();
    }


    public Result<QuestionnaireItem> AddQuestionnaireItem(
        Guid? parentItemId,
        string linkId,
        string text,
        QuestionType type,
        int orderNumber,
        bool required,
        List<EnableWhen> enableWhenConditions,
        EnableWhenBehavior? enableBehavior,
        DisabledDisplay? disabledDisplay,
        string? prefix,
        int? maxLength,
        bool readOnly,
        bool repeats,
        UiElementType uiElementType,
        List<AnswerOption> options,
        QuestionAnswer? initial,
        Dictionary<string, object>? metadata
    )
    {
        if (parentItemId.HasValue)
        {
            var parentExists = _items.Any(i => i.Id == parentItemId.Value);
            if (!parentExists)
                return QuestionnaireErrors.ParentItemNotFound(parentItemId.Value);
        }

        if (_items.Any(i => i.LinkId == linkId))
            return QuestionnaireErrors.DuplicateLinkId(linkId);

        var itemResult = QuestionnaireItem.Create(Id, parentItemId, linkId, text, type, orderNumber,
            required, enableWhenConditions, enableBehavior, disabledDisplay, prefix, maxLength, readOnly, repeats,
            uiElementType, options, initial, metadata);

        if (itemResult.IsError) return itemResult.Error;

        _items.Add(itemResult.Value);

        return itemResult;
    }

    public Result UpdateQuestionnaireItem(
        Guid questionnaireItemId,
        string linkId,
        string text,
        QuestionType type,
        int orderNumber,
        bool required,
        List<EnableWhen> enableWhenConditions,
        EnableWhenBehavior? enableBehavior,
        DisabledDisplay? disabledDisplay,
        string? prefix,
        int? maxLength,
        bool readOnly,
        bool repeats,
        UiElementType uiElementType,
        List<AnswerOption> options,
        QuestionAnswer? initial,
        Dictionary<string, object>? metadata
    )
    {
        var item = _items.FirstOrDefault(p => p.Id == questionnaireItemId);
        if (item is null) return QuestionnaireErrors.QuestionnaireItemNotFound(questionnaireItemId);

        if (linkId != item.LinkId && _items.Any(i => i.LinkId == linkId && i.Id != questionnaireItemId))
            return QuestionnaireErrors.DuplicateLinkId(linkId);

        return item.Update(linkId, text, type, orderNumber, required, enableWhenConditions, enableBehavior, disabledDisplay,
            prefix, maxLength, readOnly, repeats, uiElementType, options, initial, metadata);
    }

    public Result RemoveQuestionnaireItem(Guid questionnaireItemId)
    {
        var item = _items.FirstOrDefault(p => p.Id == questionnaireItemId);
        if (item is null) return QuestionnaireErrors.QuestionnaireItemNotFound(questionnaireItemId);

        _items.Remove(item);

        return Result.Ok();
    }

    public Result ReorderQuestionnaireItems(Dictionary<Guid, int> orderMap)
    {
        // Validate that all provided IDs exist in the questionnaire
        foreach (var itemId in orderMap.Keys.Where(itemId => !_items.Any(i => i.Id == itemId)))
        {
            return QuestionnaireErrors.QuestionnaireItemNotFound(itemId);
        }

        foreach (var item in _items.Where(i => orderMap.ContainsKey(i.Id)))
        {
            var result = item.UpdateOrderNumber(orderMap[item.Id]);
            if (result.IsError) return result;
        }

        return Result.Ok();
    }
}

public enum QuestionnaireStatus
{
    Draft = 0,
    Active = 1,
    Inactive = 2
}