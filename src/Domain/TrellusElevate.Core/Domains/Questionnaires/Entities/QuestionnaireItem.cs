using TrellusElevate.Core.Domains.Questionnaires.Mappers;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Core.Domains.Questionnaires.Entities;

public sealed class QuestionnaireItem : EntityBase<Guid>
{
    public Guid QuestionnaireId { get; private set; }
    public Guid? ParentQuestionnaireItemId { get; private set; }
    public QuestionnaireItem? ParentQuestionnaireItem { get; init; }
    public string LinkId { get; private set; } = null!;
    public string Text { get; private set; } = null!;
    public QuestionType Type { get; private set; }
    public int OrderNumber { get; private set; }
    public bool Required { get; private set; }
    public EnableWhenBehavior? EnableBehavior { get; private set; }
    public DisabledDisplay? DisabledDisplay { get; private set; }
    public string? Prefix { get; private set; }
    public int? MaxLength { get; private set; }
    public bool ReadOnly { get; private set; }
    public bool Repeats { get; private set; }
    public UiElementType UiElementType { get; private set; }
    public QuestionAnswer? Initial { get; private set; }
    public Dictionary<string, object>? Metadata { get; private set; }
    public List<EnableWhen> EnableWhen { get; private set; } = [];
    public List<AnswerOption> Options { get; private set; } = [];

    private readonly List<QuestionnaireItem> _items = [];
    public IReadOnlyList<QuestionnaireItem> Items => _items.AsReadOnly();


    private QuestionnaireItem()
    {
    }

    internal static Result<QuestionnaireItem> Create(
        Guid questionnaireId,
        Guid? parentItemId,
        string linkId,
        string text,
        QuestionType type,
        int orderNumber,
        bool required,
        List<EnableWhen> enableWhenConditions,
        EnableWhenBehavior? enableBehavior,
        DisabledDisplay? disabledDisplay,
        string? prefix,
        int? maxLength,
        bool readOnly,
        bool repeats,
        UiElementType uiElementType,
        List<AnswerOption> options,
        QuestionAnswer? initial,
        Dictionary<string, object>? metadata
    )
    {
        if (questionnaireId == Guid.Empty) return QuestionnaireErrors.InvalidQuestionnaireId(questionnaireId);
        if (string.IsNullOrWhiteSpace(linkId)) return QuestionnaireErrors.InvalidLinkId(linkId);
        if (string.IsNullOrWhiteSpace(text)) return QuestionnaireErrors.InvalidText(text);
        if (orderNumber < 0) return QuestionnaireErrors.InvalidOrderNumber(orderNumber);
        if (maxLength is <= 0) return QuestionnaireErrors.InvalidMaxLength(maxLength);

        if (initial != null && initial.Type != type.ToAnswerType())
        {
            return QuestionnaireErrors.InvalidInitialAnswer;
        }

        var item = new QuestionnaireItem
        {
            Id = Guid.CreateVersion7(),
            QuestionnaireId = questionnaireId,
            ParentQuestionnaireItemId = parentItemId,
            LinkId = linkId,
            Text = text,
            Type = type,
            OrderNumber = orderNumber,
            Required = required,
            EnableBehavior = enableBehavior,
            DisabledDisplay = disabledDisplay,
            Prefix = prefix,
            MaxLength = maxLength,
            ReadOnly = readOnly,
            Repeats = repeats,
            UiElementType = uiElementType,
            Initial = initial,
            Options = options,
            EnableWhen = enableWhenConditions,
            Metadata = metadata ?? new Dictionary<string, object>()
        };

        return item;
    }

    internal Result Update(
        string linkId,
        string text,
        QuestionType type,
        int orderNumber,
        bool required,
        List<EnableWhen> enableWhenConditions,
        EnableWhenBehavior? enableBehavior,
        DisabledDisplay? disabledDisplay,
        string? prefix,
        int? maxLength,
        bool readOnly,
        bool repeats,
        UiElementType uiElementType,
        List<AnswerOption> options,
        QuestionAnswer? initial,
        Dictionary<string, object>? metadata
    )
    {
        if (string.IsNullOrWhiteSpace(linkId)) return QuestionnaireErrors.InvalidLinkId(linkId);
        if (string.IsNullOrWhiteSpace(text)) return QuestionnaireErrors.InvalidText(text);
        if (orderNumber < 0) return QuestionnaireErrors.InvalidOrderNumber(orderNumber);
        if (maxLength is <= 0) return QuestionnaireErrors.InvalidMaxLength(maxLength);

        if (initial != null && initial.Type != Type.ToAnswerType())
        {
            return QuestionnaireErrors.InvalidInitialAnswer;
        }

        LinkId = linkId;
        Text = text;
        Type = type;
        OrderNumber = orderNumber;
        Required = required;
        EnableBehavior = enableBehavior;
        DisabledDisplay = disabledDisplay;
        Prefix = prefix;
        MaxLength = maxLength;
        ReadOnly = readOnly;
        Repeats = repeats;
        UiElementType = uiElementType;
        Initial = initial;
        Options = options;
        EnableWhen = enableWhenConditions;
        Metadata = metadata ?? new Dictionary<string, object>();

        return Result.Ok();
    }

    internal Result UpdateOrderNumber(int orderNumber)
    {
        if (orderNumber < 0) return QuestionnaireErrors.InvalidOrderNumber(orderNumber);
        OrderNumber = orderNumber;
        return Result.Ok();
    }
}

public enum EnableWhenBehavior
{
    /// <summary>
    /// Enable the question when all the enableWhen criteria are satisfied.
    /// </summary>
    All,

    /// <summary>
    /// Enable the question when any of the enableWhen criteria are satisfied.
    /// </summary>
    Any
}

public enum DisabledDisplay
{
    /// <summary>
    /// The item (and its children) should not be visible to the user at all.
    /// </summary>
    Hidden,

    /// <summary>
    /// The item (and possibly its children) should not be selectable or editable but should still be visible - to allow the user to see what questions could have been completed had other answers caused the item to be enabled.
    /// </summary>
    Protected
}

public enum QuestionType
{
    Group = 0,
    Display = 1,
    Boolean = 2,
    Decimal = 3,
    Integer = 4,
    Date = 5,
    DateTime = 6,
    Time = 7,
    String = 8,
    Text = 9,
    Url = 10
}

public enum AnswerType
{
    Boolean = 1,
    Decimal = 2,
    Integer = 3,
    Date = 4,
    DateTime = 5,
    Time = 6,
    String = 7,
    Text = 8,
    Url = 9
}

public enum Operator
{
    Equals = 0,
    Exists = 1,
    NotEquals = 2,
    GreaterThan = 3,
    GreaterThanOrEquals = 4,
    LessThan = 5,
    LessThanOrEquals = 6,
    Contains = 7,
    NotContains = 8
}

public enum UiElementType
{
    /// <summary>
    /// Default for Question/answer type. eg. dropdown for question with options
    /// </summary>
    Default = 0,

    /// <summary>
    /// Using a slider to select value in case of question with options
    /// </summary>
    Slider = 1,

    /// <summary>
    /// Using a list to select value in case of question with options
    /// </summary>
    List = 2
}