using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Core.Domains.Questionnaires.Common.Helpers;

public static class QuestionnaireHelpers
{
    public static bool IsAnswerableType(this QuestionType? questionType) =>
        questionType is not (null or QuestionType.Group or QuestionType.Display);
    
    public static bool IsAnswerableType(this QuestionType questionType) =>
        questionType is not (QuestionType.Group or QuestionType.Display);
}