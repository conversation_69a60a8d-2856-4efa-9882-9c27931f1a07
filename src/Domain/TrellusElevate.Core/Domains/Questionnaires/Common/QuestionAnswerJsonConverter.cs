using System.Text.Json;
using System.Text.Json.Serialization;
using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Core.Domains.Questionnaires.Common;

public class QuestionAnswerJsonConverter : JsonConverter<QuestionAnswer>
{
    public override QuestionAnswer Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        using var doc = JsonDocument.ParseValue(ref reader);
        var root = doc.RootElement;

        // 1) read the Type

        if (!(root.TryGetProperty("Type", out var typeProperty) || root.TryGetProperty("type", out typeProperty)))
        {
            throw new ArgumentException("Type not found");
        }
        
        var typeCode = typeProperty.GetInt32();
        var answerType = (AnswerType)typeCode;

        // 2) read the raw Value
        
        if (!(root.TryGetProperty("Value", out var elem) || root.TryGetProperty("value", out elem)))
        {
            throw new ArgumentException("Value not found");
        }

        // 3) switch on AnswerType to pick the right CLR type
        object? value = elem.ValueKind == JsonValueKind.Null
            ? null
            : answerType switch
            {
                AnswerType.Boolean => elem.GetBoolean(),
                AnswerType.Integer => elem.GetInt32(),
                AnswerType.Decimal => elem.GetDecimal(),
                AnswerType.Date => DateOnly.Parse(elem.GetString()!),
                AnswerType.DateTime => elem.GetDateTime(),
                AnswerType.Time => TimeOnly.Parse(elem.GetString()!),
                // fallbacks for string‑based types:
                AnswerType.String
                    or AnswerType.Text
                    or AnswerType.Url => elem.GetString(),
                _ => elem.GetString()
            };

        return new QuestionAnswer(value, answerType);
    }

    public override void Write(Utf8JsonWriter writer, QuestionAnswer qa, JsonSerializerOptions options)
    {
        writer.WriteStartObject();

        writer.WriteNumber("type", (int)qa.Type!);

        writer.WritePropertyName("value");
        // let the serializer emit the correct JSON for whatever CLR type you hold
        JsonSerializer.Serialize(writer, qa.Value, qa.Value?.GetType() ?? typeof(object), options);

        writer.WriteEndObject();
    }
}