namespace TrellusElevate.Core.Domains.Questionnaires;

public static class QuestionnaireErrors
{
    public static Error Exists(string name) =>
        Error.Conflict("Questionnaire.Exists", $"Questionnaire with name: {name} already exists");
    public static Error InvalidName(string name) =>
        Error.Validation("Questionnaire.InvalidName", $"Invalid name: {name}");

    public static Error InvalidTitle(string title) =>
        Error.Validation("Questionnaire.InvalidTitle", $"Invalid title: {title}");
    
    public static Error InvalidLinkId(string linkId) =>
        Error.Validation("Questionnaire.InvalidLinkId", $"Invalid link id: {linkId}");
    
    public static Error InvalidText(string text) =>
        Error.Validation("Questionnaire.InvalidText", $"Invalid text: {text}");
    
    public static Error InvalidOrderNumber(int orderNumber) =>
        Error.Validation("Questionnaire.InvalidOrderNumber", $"Invalid order number: {orderNumber}");
    
    public static Error InvalidMaxLength(int? maxLength) =>
        Error.Validation("Questionnaire.InvalidMaxLength", $"Invalid max length: {maxLength}");

    public static Error InvalidQuestionnaireId(Guid questionnaireId) =>
        Error.Validation("Questionnaire.InvalidQuestionnaireId", $"Invalid questionnaire id: {questionnaireId}");

    public static Error NotFound(Guid questionnaireId) =>
        Error.NotFound("Questionnaire.NotFound", $"Questionnaire: {questionnaireId} not found");

    public static Error QuestionnaireItemNotFound(Guid questionnaireItemId) =>
        Error.NotFound("Questionnaire.QuestionnaireItemNotFound",
            $"Questionnaire item: {questionnaireItemId} not found");

    public static Error ParentItemNotFound(Guid parentItemId) => Error.NotFound("Questionnaire.ParentItemNotFound",
        $"Parent questionnaire item with ID '{parentItemId}' was not found.");

    public static Error DuplicateLinkId(string linkId) => Error.Conflict("Questionnaire.DuplicateLinkId",
        $"An item with Link ID '{linkId}' already exists in questionnaire.");
    
    public static Error InvalidInitialAnswer  => Error.Problem(
        "Questionnaire.InvalidInitialAnswer", "Initial answer type does not match the question type.");
    
}