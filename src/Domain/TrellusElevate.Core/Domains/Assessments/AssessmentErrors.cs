namespace TrellusElevate.Core.Domains.Assessments;

public static class AssessmentErrors
{
    public static Error NotFound(Guid assessmentId) => Error.NotFound(
        "Assessment.NotFound", $"Assessment: {assessmentId} not found");
    public static Error InvalidName(string name) => Error.Validation
        ("Assessment.InvalidName", $"Invalid name: {name}");

    public static Error InvalidQuestionnaireId(Guid questionnaireId) => Error.Validation
        ("Assessment.InvalidQuestionnaireId", $"Invalid questionnaire id: {questionnaireId}");

    public static Error CannotUpdateQuestionnaireForActiveAssessment => Error.Problem(
        "Assessment.CannotUpdateQuestionnaireForActiveAssessment",
        $"Cannot change questionnaire for active assessment");

    public static Error ScoringProfileNotFound(Guid scoringProfileId) => Error.NotFound(
        "Assessment.ScoringProfileNotFound", $"Scoring profile: {scoringProfileId} not found");

    public static Error InvalidAssessmentId(Guid assessmentId) => Error.Validation("Assessment.InvalidAssessmentId",
        $"Invalid assessment id: {assessmentId}");

    public static Error InvalidScoringProfileId(Guid scoringProfileId) =>
        Error.Validation("Assessment.InvalidScoringProfileId", $"Invalid scoring profile id: {scoringProfileId}");

    public static Error InvalidQuestionnaireItemId(Guid questionnaireItemId) => Error.Validation(
        "Assessment.InvalidQuestionnaireItemId", $"Invalid questionnaire item id: {questionnaireItemId}");

    public static Error InvalidScoringProfileName(string name) =>
        Error.Validation("Assessment.InvalidScoringProfileName", $"Invalid scoring profile name: {name}");

    public static Error InvalidCalculationFormula(string calculationFormula) =>
        Error.Validation("Assessment.InvalidCalculationFormula", $"Invalid calculation formula: {calculationFormula}");

    public static Error ScoreMappingNotFound(Guid scoreMappingId) =>
        Error.NotFound("Assessment.ScoreMappingNotFound", $"Score mapping: {scoreMappingId} not found");
    
    public static Error DuplicateScoreMapping =>
        Error.Conflict("Assessment.DuplicateScoreMapping", "Duplicate Score mapping");

    public static Error InvalidAnswers =>
        Error.Validation("Assessment.InvalidAnswers", "Answers cannot be null or empty");
}