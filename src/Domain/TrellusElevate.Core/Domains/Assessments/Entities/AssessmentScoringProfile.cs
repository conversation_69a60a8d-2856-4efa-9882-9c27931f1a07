using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Core.Domains.Assessments.Entities;

public sealed class AssessmentScoringProfile : EntityBase<Guid>
{
    public Guid AssessmentId { get; private set; }
    public string Name { get; private set; } = null!;
    public string? Description { get; private set; }
    public string? CalculationFormula { get; private set; }
    public AssessmentScoringProfileStatus Status { get; private set; }

    private readonly List<AssessmentScoreMapping> _scoreMappings = [];
    public IReadOnlyCollection<AssessmentScoreMapping> ScoreMappings => _scoreMappings.AsReadOnly();

    private AssessmentScoringProfile()
    {
    }

    internal static Result<AssessmentScoringProfile> Create(Guid assessmentId, string name, string? description)
    {
        if (assessmentId == Guid.Empty) return AssessmentErrors.InvalidAssessmentId(assessmentId);
        if (string.IsNullOrWhiteSpace(name)) return AssessmentErrors.InvalidScoringProfileName(name);

        return new AssessmentScoringProfile
        {
            Id = Guid.CreateVersion7(),
            AssessmentId = assessmentId,
            Name = name,
            Description = description,
            Status = AssessmentScoringProfileStatus.Draft
        };
    }

    internal Result Update(string name, string? description, string? calculationFormula,
        AssessmentScoringProfileStatus status)
    {
        if (string.IsNullOrWhiteSpace(name)) return AssessmentErrors.InvalidScoringProfileName(name);

        Name = name;
        Description = description;
        CalculationFormula = calculationFormula;
        Status = status;

        return Result.Ok();
    }

    internal Result<AssessmentScoreMapping> AddScoreMapping(Guid questionnaireItemId, List<QuestionAnswer> answers,
        Operator @operator, double weight)
    {
        if (questionnaireItemId == Guid.Empty) return AssessmentErrors.InvalidQuestionnaireItemId(questionnaireItemId);

        // Check for duplicates - compare lists of answers
        if (_scoreMappings.Any(m => m.QuestionnaireItemId == questionnaireItemId &&
                                    AreAnswerListsEqual(m.Answers, answers) &&
                                    m.Operator == @operator))
        {
            return AssessmentErrors.DuplicateScoreMapping;
        }

        var mappingResult = AssessmentScoreMapping.Create(Id, questionnaireItemId, answers, @operator, weight);
        if (mappingResult.IsError) return mappingResult.Error;

        _scoreMappings.Add(mappingResult.Value);

        return mappingResult.Value;
    }

    internal Result UpdateScoreMapping(Guid scoreMappingId, List<QuestionAnswer> answers, Operator @operator, double weight)
    {
        var mapping = _scoreMappings.FirstOrDefault(m => m.Id == scoreMappingId);
        if (mapping == null) return AssessmentErrors.ScoreMappingNotFound(scoreMappingId);

        // Check for duplicates - compare lists of answers
        if (_scoreMappings.Any(m => m.Id != scoreMappingId &&
                                    m.QuestionnaireItemId == mapping.QuestionnaireItemId &&
                                    AreAnswerListsEqual(m.Answers, answers) &&
                                    m.Operator == @operator))
        {
            return AssessmentErrors.DuplicateScoreMapping;
        }

        var result = mapping.Update(answers, @operator, weight);

        return result.IsError ? result : Result.Ok();
    }

    internal Result RemoveScoreMapping(Guid scoreMappingId)
    {
        var mapping = _scoreMappings.FirstOrDefault(m => m.Id == scoreMappingId);
        if (mapping == null) return AssessmentErrors.ScoreMappingNotFound(scoreMappingId);

        _scoreMappings.Remove(mapping);

        return Result.Ok();
    }

    private static bool AreAnswerListsEqual(List<QuestionAnswer> list1, List<QuestionAnswer> list2)
    {
        if (list1.Count != list2.Count) return false;

        // Sort both lists for comparison (by value and type)
        var sorted1 = list1.OrderBy(x => x.Value?.ToString()).ThenBy(x => x.Type).ToList();
        var sorted2 = list2.OrderBy(x => x.Value?.ToString()).ThenBy(x => x.Type).ToList();

        return sorted1.SequenceEqual(sorted2);
    }
}

public enum AssessmentScoringProfileStatus
{
    Draft = 0,
    Active = 1,
    Inactive = 2
}