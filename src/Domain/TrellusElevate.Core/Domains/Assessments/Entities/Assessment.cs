using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Core.Domains.Assessments.Entities;

public sealed class Assessment : EntityBase<Guid>, IAggregateRoot
{
    public string Name { get; private set; } = null!;
    public string? Description { get; private set; }
    public Guid QuestionnaireId { get; private set; }
    public Questionnaire? Questionnaire { get; init; }
    public AssessmentStatus Status { get; private set; }

    private readonly List<AssessmentScoringProfile> _scoringProfiles = [];
    public IReadOnlyCollection<AssessmentScoringProfile> ScoringProfiles => _scoringProfiles.AsReadOnly();

    private Assessment()
    {
    }

    public static Result<Assessment> Create(string name, string? description, Guid questionnaireId)
    {
        if (string.IsNullOrWhiteSpace(name)) return AssessmentErrors.InvalidName(name);
        if (questionnaireId == Guid.Empty) return AssessmentErrors.InvalidQuestionnaireId(questionnaireId);

        return new Assessment
        {
            Id = Guid.CreateVersion7(),
            Name = name,
            Description = description,
            QuestionnaireId = questionnaireId,
            Status = AssessmentStatus.Draft,
        };
    }

    public Result Update(string name, string description, AssessmentStatus status)
    {
        if (string.IsNullOrWhiteSpace(name)) return AssessmentErrors.InvalidName(name);

        Name = name;
        Description = description;
        Status = status;

        return Result.Ok();
    }

    public Result UpdateQuestionnaire(Guid questionnaireId)
    {
        if (QuestionnaireId == questionnaireId) return Result.Ok();

        if (questionnaireId == Guid.Empty) return AssessmentErrors.InvalidQuestionnaireId(questionnaireId);

        if (Status == AssessmentStatus.Active || _scoringProfiles.Any(s => s.ScoreMappings.Count != 0))
            return AssessmentErrors.CannotUpdateQuestionnaireForActiveAssessment;

        QuestionnaireId = questionnaireId;

        return Result.Ok();
    }

    public Result<AssessmentScoringProfile> AddScoringProfile(string name, string? description)
    {
        var profileResult = AssessmentScoringProfile.Create(Id, name, description);

        if (profileResult.IsError) return profileResult.Error;

        _scoringProfiles.Add(profileResult.Value);

        return profileResult;
    }

    public Result UpdateScoringProfile(Guid scoringProfileId, string name, string? description,
        string? calculationFormula,
        AssessmentScoringProfileStatus status)
    {
        var profile = _scoringProfiles.FirstOrDefault(p => p.Id == scoringProfileId);
        if (profile == null) return AssessmentErrors.ScoringProfileNotFound(scoringProfileId);

        return profile.Update(name, description, calculationFormula, status);
    }

    public Result RemoveScoringProfile(Guid scoringProfileId)
    {
        var profile = _scoringProfiles.FirstOrDefault(p => p.Id == scoringProfileId);
        if (profile == null) return AssessmentErrors.ScoringProfileNotFound(scoringProfileId);

        _scoringProfiles.Remove(profile);

        return Result.Ok();
    }

    public Result<AssessmentScoreMapping> AddScoreMapping(Guid scoringProfileId, Guid questionnaireItemId,
        List<QuestionAnswer> answers,
        Operator @operator, double weight)
    {
        var scoringProfile = _scoringProfiles.FirstOrDefault(p => p.Id == scoringProfileId);

        if (scoringProfile == null) return AssessmentErrors.ScoringProfileNotFound(scoringProfileId);

        return scoringProfile.AddScoreMapping(questionnaireItemId, answers, @operator, weight);
    }

    public Result UpdateScoreMapping(Guid scoringProfileId, Guid scoreMappingId, List<QuestionAnswer> answers,
        Operator @operator, double weight)
    {
        var scoringProfile = _scoringProfiles.FirstOrDefault(p => p.Id == scoringProfileId);

        if (scoringProfile == null) return AssessmentErrors.ScoringProfileNotFound(scoringProfileId);

        return scoringProfile.UpdateScoreMapping(scoreMappingId, answers, @operator, weight);
    }

    public Result RemoveScoreMapping(Guid scoringProfileId, Guid scoreMappingId)
    {
        var scoringProfile = _scoringProfiles.FirstOrDefault(p => p.Id == scoringProfileId);

        if (scoringProfile == null) return AssessmentErrors.ScoringProfileNotFound(scoringProfileId);

        return scoringProfile.RemoveScoreMapping(scoreMappingId);
    }
}

public enum AssessmentStatus
{
    Draft = 0,
    Active = 1,
    Inactive = 2
}