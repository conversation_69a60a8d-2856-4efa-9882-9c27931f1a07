using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Core.Domains.Assessments.Entities;

public sealed class AssessmentScoreMapping : EntityBase<Guid>
{
    public Guid AssessmentScoringProfileId { get; private set; }
    public Guid QuestionnaireItemId { get; private set; }
    public List<QuestionAnswer> Answers { get; private set; } = null!;
    public Operator Operator { get; private set; }
    public double Weight { get; private set; }

    private AssessmentScoreMapping()
    {
    }

    internal static Result<AssessmentScoreMapping> Create(Guid assessmentScoringProfileId, Guid questionnaireItemId,
        List<QuestionAnswer> answers, Operator @operator, double weight)
    {
        if (assessmentScoringProfileId == Guid.Empty) return AssessmentErrors.InvalidScoringProfileId(assessmentScoringProfileId);
        if (questionnaireItemId == Guid.Empty) return AssessmentErrors.InvalidQuestionnaireItemId(questionnaireItemId);
        if (answers == null || answers.Count == 0) return AssessmentErrors.InvalidAnswers;

        return new AssessmentScoreMapping
        {
            Id = Guid.CreateVersion7(),
            AssessmentScoringProfileId = assessmentScoringProfileId,
            QuestionnaireItemId = questionnaireItemId,
            Answers = answers,
            Operator = @operator,
            Weight = weight
        };
    }

    internal Result Update(List<QuestionAnswer> answers, Operator @operator, double weight)
    {
        if (answers == null || answers.Count == 0) return AssessmentErrors.InvalidAnswers;

        Answers = answers;
        Operator = @operator;
        Weight = weight;

        return Result.Ok();
    }
}