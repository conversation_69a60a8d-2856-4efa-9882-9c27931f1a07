using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

namespace TrellusElevate.Core.Domains.AssessmentResponses.Entities;

public sealed class AssessmentResponse : EntityBase<Guid>, IAggregateRoot
{
    public Guid AssessmentId { get; private set; }
    public Assessment Assessment { get; init; } = null!;
    public Guid QuestionnaireResponseId { get; private set; }
    public QuestionnaireResponse? QuestionnaireResponse { get; init; }

    private readonly List<AssessmentResponseScore> _scores = [];
    public IReadOnlyCollection<AssessmentResponseScore> Scores => _scores.AsReadOnly();

    private AssessmentResponse()
    {
    }

    public static Result<AssessmentResponse> Create(Guid assessmentId, Guid questionnaireResponseId)
    {
        return new AssessmentResponse
        {
            Id = Guid.CreateVersion7(),
            AssessmentId = assessmentId,
            QuestionnaireResponseId = questionnaireResponseId
        };
    }

    public Result<AssessmentResponseScore> AddScore(Guid assessmentScoringProfileId, double value)
    {
        var scoreResult = AssessmentResponseScore.Create(Id, assessmentScoringProfileId, value);
        if (scoreResult.IsError) return scoreResult.Error;
        _scores.Add(scoreResult.Value);
        return scoreResult.Value;
    }
}