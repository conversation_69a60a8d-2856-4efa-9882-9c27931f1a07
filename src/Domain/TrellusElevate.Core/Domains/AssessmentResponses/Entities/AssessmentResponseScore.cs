using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Core.Domains.AssessmentResponses.Entities;

public sealed class AssessmentResponseScore : EntityBase<Guid>
{
    public Guid AssessmentResponseId { get; private set; }
    public Guid AssessmentScoringProfileId { get; private set; }
    public AssessmentScoringProfile AssessmentScoringProfile { get; set; } = null!;
    public double Value { get; private set; }

    private AssessmentResponseScore()
    {
    }

    internal static Result<AssessmentResponseScore> Create(Guid assessmentResponseId, Guid assessmentScoringProfileId,
        double value)
    {
        return new AssessmentResponseScore
        {
            Id = Guid.CreateVersion7(),
            AssessmentResponseId = assessmentResponseId,
            AssessmentScoringProfileId = assessmentScoringProfileId,
            Value = value
        };
    }
}