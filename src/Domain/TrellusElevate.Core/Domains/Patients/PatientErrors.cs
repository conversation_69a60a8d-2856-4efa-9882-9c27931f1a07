namespace TrellusElevate.Core.Domains.Patients;

public static class PatientErrors
{
    public static Error NotFound(Guid patientId) =>
        Error.NotFound("Patient.NotFound", $"Patient with id: {patientId} was not found");
    
    public static Error PatientAssessmentNotFound(Guid patientAssessmentId) =>
        Error.NotFound("Patient.PatientAssessmentNotFound",
            $"Patient Assessment with id: {patientAssessmentId} was not found");
}