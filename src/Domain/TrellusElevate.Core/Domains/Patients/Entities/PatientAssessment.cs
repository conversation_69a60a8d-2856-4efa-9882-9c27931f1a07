using TrellusElevate.Core.Domains.AssessmentResponses.Entities;
using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Core.Domains.Patients.Entities;

public sealed class PatientAssessment : EntityBase<Guid>
{
    public Guid PatientId { get; private set; }
    public Guid AssessmentId { get; private set; }
    public Assessment? Assessment { get; init; }
    public Guid? AssessmentResponseId { get; private set; }
    public AssessmentResponse? AssessmentResponse { get; init; }
    public PatientAssessmentStatus Status { get; private set; }
    public DateTimeOffset? CompletedAt { get; private set; }

    private PatientAssessment()
    {
    }

    public static Result<PatientAssessment> Create(Guid patientId, Guid assessmentId)
    {
        return new PatientAssessment
        {
            Id = Guid.NewGuid(),
            PatientId = patientId,
            AssessmentId = assessmentId,
            Status = PatientAssessmentStatus.Pending
        };
    }

    public Result Complete(Guid assessmentResponseId, DateTimeOffset? completedAt = null)
    {
        AssessmentResponseId = assessmentResponseId;
        Status = PatientAssessmentStatus.Completed;
        CompletedAt = completedAt ?? DateTimeOffset.UtcNow;
        return Result.Ok();
    }

    public void Cancel()
    {
        Status = PatientAssessmentStatus.Cancelled;
    }

    public void Skip()
    {
        Status = PatientAssessmentStatus.Skipped;
    }
}

public enum PatientAssessmentStatus
{
    Pending = 0,
    Completed = 1,
    Skipped = 2,
    Cancelled = 3
}