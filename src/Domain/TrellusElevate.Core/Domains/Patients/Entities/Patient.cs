using System.ComponentModel;
using TrellusElevate.Core.Common.Enums;

namespace TrellusElevate.Core.Domains.Patients.Entities;

public sealed class Patient : EntityBase<Guid>, IAggregateRoot
{
    public string FirstName { get; private set; } = null!;
    public string? MiddleName { get; private set; }
    public string LastName { get; private set; } = null!;
    public DateOnly BirthDate { get; private set; }
    public Gender Gender { get; private set; }
    public PatientStatus Status { get; private set; }
    public string ContactEmail { get; private set; } = null!;
    public string ContactPhone { get; private set; } = null!;

    private readonly List<PatientAssessment> _assessments = [];
    public IReadOnlyCollection<PatientAssessment> Assessments => _assessments.AsReadOnly();

    private Patient()
    {
    }

    public static Result<Patient> Create(string firstName, string? middleName, string lastName, DateOnly birthDate,
        Gender gender, PatientStatus status, string contactEmail, string contactPhone)
    {
        return new Patient
        {
            Id = Guid.CreateVersion7(),
            FirstName = firstName,
            MiddleName = middleName,
            LastName = lastName,
            BirthDate = birthDate,
            Gender = gender,
            Status = status,
            ContactEmail = contactEmail,
            ContactPhone = contactPhone
        };
    }

    public Result Update(string firstName, string? middleName, string lastName, DateOnly birthDate,
        Gender gender, string contactEmail, string contactPhone)
    {
        FirstName = firstName;
        MiddleName = middleName;
        LastName = lastName;
        BirthDate = birthDate;
        Gender = gender;
        ContactEmail = contactEmail;
        ContactPhone = contactPhone;

        return Result.Ok();
    }

    public Result UpdateStatus(PatientStatus status)
    {
        Status = status;
        return Result.Ok();
    }

    public Result<PatientAssessment> AddAssessment(Guid assessmentId)
    {
        var result = PatientAssessment.Create(Id, assessmentId);
        if (result.IsError) return result.Error;
        _assessments.Add(result.Value);
        return result.Value;
    }

    public Result CompleteAssessment(Guid patientAssessmentId, Guid assessmentResponseId,
        DateTimeOffset? completedAt = null)
    {
        var assessment = _assessments.FirstOrDefault(pa => pa.Id == patientAssessmentId);
        if (assessment is null) return PatientErrors.PatientAssessmentNotFound(patientAssessmentId);
        assessment.Complete(assessmentResponseId, completedAt);
        return Result.Ok();
    }

    public Result CancelAssessment(Guid patientAssessmentId)
    {
        var assessment = _assessments.FirstOrDefault(pa => pa.Id == patientAssessmentId);
        if (assessment is null) return PatientErrors.PatientAssessmentNotFound(patientAssessmentId);
        assessment.Cancel();
        return Result.Ok();
    }

    public Result SkipPatientAssessment(Guid patientAssessmentId)
    {
        var assessment = _assessments.FirstOrDefault(pa => pa.Id == patientAssessmentId);
        if (assessment is null) return PatientErrors.PatientAssessmentNotFound(patientAssessmentId);
        assessment.Skip();
        return Result.Ok();
    }
}

public enum PatientStatus
{
    [Description("Pre-registered")] PreRegistered,
    [Description("Registered")] Registered,
    [Description("Paused")] Paused,
    [Description("Discontinued")] Discontinued,
    [Description("Archived")] Archived
}