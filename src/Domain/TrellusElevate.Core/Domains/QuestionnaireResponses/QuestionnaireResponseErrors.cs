namespace TrellusElevate.Core.Domains.QuestionnaireResponses;

public static class QuestionnaireResponseErrors
{
    public static Error InvalidQuestionnaireItemId(Guid questionnaireId) =>
        Error.Validation("QuestionnaireResponse.InvalidQuestionnaireItemId", $"Invalid questionnaire item id: {questionnaireId}");
    
    public static Error InvalidQuestionnaireResponseId(Guid questionnaireResponseId) =>
        Error.Validation("QuestionnaireResponse.InvalidQuestionnaireResponseId", $"Invalid questionnaire response id: {questionnaireResponseId}");
    
    public static Error EmptyAnswers =>
        Error.Validation("QuestionnaireResponse.EmptyAnswers", "Answers value cannot be empty");
    
    public static Error ParentResponseItemNotFound(Guid parentItemId) => Error.NotFound(
        "QuestionnaireResponse.ParentResponseItemNotFound",
        $"Parent questionnaire response item with ID '{parentItemId}' was not found.");
    
    public static Error DuplicateQuestionnaireItemId(Guid questionnaireItemId) => Error.Conflict(
        "QuestionnaireResponse.DuplicateLinkId",
        $"An item with QuestionnaireItem ID '{questionnaireItemId}' already exists in questionnaire.");
    
}