using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

public sealed class QuestionnaireResponseItem : EntityBase<Guid>
{
    public Guid QuestionnaireResponseId { get; private set; }
    public Guid? ParentQuestionnaireResponseItemId { get; private set; }
    public QuestionnaireResponseItem? ParentQuestionnaireResponseItem { get; init; }
    
    public Guid QuestionnaireItemId { get; private set; }
    public QuestionnaireItem? QuestionnaireItem { get; init; }
    
    public List<QuestionAnswer> Answers { get; private set; } = null!;

    private readonly List<QuestionnaireResponseItem> _items = [];
    public IReadOnlyList<QuestionnaireResponseItem> Items => _items.AsReadOnly();

    internal static Result<QuestionnaireResponseItem> Create(
        Guid questionnaireResponseId,
        Guid questionnaireItemId,
        Guid? parentQuestionnaireResponseItemId,
        List<QuestionAnswer> answers)
    {
        if (questionnaireResponseId == Guid.Empty)
            return QuestionnaireResponseErrors.InvalidQuestionnaireResponseId(questionnaireResponseId);
        if (questionnaireItemId == Guid.Empty) return QuestionnaireResponseErrors.InvalidQuestionnaireItemId(questionnaireItemId);

        return new QuestionnaireResponseItem
        {
            Id = Guid.CreateVersion7(),
            QuestionnaireResponseId = questionnaireResponseId,
            QuestionnaireItemId = questionnaireItemId,
            ParentQuestionnaireResponseItemId = parentQuestionnaireResponseItemId,
            Answers = answers
        };
    }

    internal Result Update(Guid? parentQuestionnaireResponseItemId, List<QuestionAnswer> answers)
    {
        if (answers.Count == 0) return QuestionnaireResponseErrors.EmptyAnswers;

        ParentQuestionnaireResponseItemId = parentQuestionnaireResponseItemId;
        Answers = answers;

        return Result.Ok();
    }
}