using TrellusElevate.Core.Domains.Questionnaires;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

public sealed class QuestionnaireResponse : EntityBase<Guid>, IAggregateRoot
{
    public Guid QuestionnaireId { get; private set; }
    public QuestionnaireResponseStatus Status { get; private set; }

    private readonly List<QuestionnaireResponseItem> _items = [];
    public IReadOnlyCollection<QuestionnaireResponseItem> Items => _items.AsReadOnly();

    public static Result<QuestionnaireResponse> Create(Guid questionnaireId, QuestionnaireResponseStatus status)
    {
        if (questionnaireId == Guid.Empty) return QuestionnaireErrors.InvalidQuestionnaireId(questionnaireId);

        return new QuestionnaireResponse
        {
            Id = Guid.CreateVersion7(),
            QuestionnaireId = questionnaireId,
            Status = status
        };
    }

    public Result Update(QuestionnaireResponseStatus status)
    {
        Status = status;

        return Result.Ok();
    }

    public Result<QuestionnaireResponseItem> AddQuestionnaireResponseItem(Guid questionnaireItemId,
        Guid? parentQuestionnaireResponseItemId, List<QuestionAnswer> answers)
    {
        if (parentQuestionnaireResponseItemId.HasValue)
        {
            if (!_items.Any(i => i.Id == parentQuestionnaireResponseItemId.Value))
                return QuestionnaireResponseErrors.ParentResponseItemNotFound(parentQuestionnaireResponseItemId.Value);
        }

        if (_items.Any(i => i.QuestionnaireItemId == questionnaireItemId))
            return QuestionnaireResponseErrors.DuplicateQuestionnaireItemId(questionnaireItemId);

        var item = QuestionnaireResponseItem.Create(Id, questionnaireItemId, parentQuestionnaireResponseItemId,
            answers);

        if (item.IsError) return item.Error;

        _items.Add(item.Value);

        return item.Value;
    }
}

public enum QuestionnaireResponseStatus
{
    InProgress = 0,
    Completed = 1,
    Amended = 2,
    Stopped = 3
}