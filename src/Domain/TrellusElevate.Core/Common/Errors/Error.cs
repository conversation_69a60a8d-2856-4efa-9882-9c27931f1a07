namespace TrellusElevate.Core.Common.Errors;

public sealed record Error
{
    public string Code { get; }
    public string Description { get; }
    public ErrorType Type { get; }
    public Dictionary<string, object?>? Extensions { get; }

    private Error(string code, string description, ErrorType type, Dictionary<string, object?>? extensions = null)
    {
        Code = code;
        Description = description;
        Type = type;
        Extensions = extensions;
    }

    public static Error Failure(string code, string description, Dictionary<string, object?>? extensions = null) =>
        new(code, description, ErrorType.Failure, extensions);

    public static Error NotFound(string code, string description, Dictionary<string, object?>? extensions = null) =>
        new(code, description, ErrorType.NotFound, extensions);

    public static Error Problem(string code, string description, Dictionary<string, object?>? extensions = null) =>
        new(code, description, ErrorType.Problem, extensions);

    public static Error Conflict(string code, string description, Dictionary<string, object?>? extensions = null) =>
        new(code, description, ErrorType.Conflict, extensions);

    public static Error Validation(string code, string description, Dictionary<string, object?>? extensions = null) =>
        new(code, description, ErrorType.Validation, extensions);
}