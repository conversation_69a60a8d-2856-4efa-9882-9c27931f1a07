<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    
    <ItemGroup>
        <PackageReference Include="Ardalis.Specification" Version="9.2.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.7" />
        <PackageReference Include="NCalcSync" Version="5.5.0" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="Destructurama.Attributed" Version="5.1.0" />
        <PackageReference Include="Mediator.Abstractions" Version="3.0.*-*" />
        <PackageReference Include="Mediator.SourceGenerator" Version="3.0.*-*">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
        </PackageReference>
    </ItemGroup>
    
    <ItemGroup>
      <ProjectReference Include="..\..\Domain\TrellusElevate.Core\TrellusElevate.Core.csproj" />
      <ProjectReference Include="..\TrellusElevate.Resources\TrellusElevate.Resources.csproj" />
    </ItemGroup>
    
</Project>
