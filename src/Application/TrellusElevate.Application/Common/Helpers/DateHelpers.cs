namespace TrellusElevate.Application.Common.Helpers;

public static class DateHelpers
{
    public static int? GetYearsFromDate(DateTime? start)
    {
        if (!start.HasValue) return null;
        var end = DateTime.UtcNow;
        return end.Year - start.Value.Year - 1 +
               (end.Month > start.Value.Month ||
                (end.Month == start.Value.Month && end.Day >= start.Value.Day)
                   ? 1
                   : 0);
    }
    
    public static int? GetDaysFromDate(DateTime? start)
    {
        if (!start.HasValue) return null;
        return (int)(DateTime.UtcNow - start!.Value).TotalDays;
    }
}