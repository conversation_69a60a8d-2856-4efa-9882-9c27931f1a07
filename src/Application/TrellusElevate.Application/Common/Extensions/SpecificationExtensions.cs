using System.Linq.Expressions;

namespace TrellusElevate.Application.Common.Extensions;

public static class SpecificationExtensions
{
    public static IOrderedSpecificationBuilder<T> ApplyOrdering<T>(
        this ISpecificationBuilder<T> builder, Expression<Func<T, object?>> orderBy,
        SortDirection sortDirection)
    {
        return sortDirection == SortDirection.Desc ? builder.OrderByDescending(orderBy) : builder.OrderBy(orderBy);
    }
}