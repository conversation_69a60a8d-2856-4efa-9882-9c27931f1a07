namespace TrellusElevate.Application.Common.Extensions;

public static class LinqExtensions
{
    /// <summary>
    /// Flattens source by childPropertySelector.
    /// </summary>
    public static IEnumerable<T> Flatten<T>(this IEnumerable<T>? source, Func<T, IEnumerable<T>?> childPropertySelector)
    {
        if (source == null) yield break;
        foreach (var item in source)
        {
            yield return item;

            foreach (var childItem in childPropertySelector(item)?.Flatten(childPropertySelector) ?? [])
            {
                yield return childItem;
            }
        }
    }
}