namespace TrellusElevate.Application.Common.Extensions;

public static class DateTimeExtensions
{
    public static DateOnly ToDateOnly(this DateTime dateTime) => DateOnly.FromDateTime(dateTime);

    public static DateOnly? ToDateOnly(this DateTime? dateTime) =>
        dateTime.HasValue ? DateOnly.FromDateTime(dateTime.Value) : null;

    public static DateTime? SetTime(in this DateTime? date, TimeSpan? time)
    {
        if (!date.HasValue) return null;
        if (!time.HasValue) return date;
        return new DateTime(date.Value.Year, date.Value.Month, date.Value.Day, time.Value.Hours, time.Value.Minutes, time.Value.Seconds,
            time.Value.Milliseconds, date.Value.Kind);
    }
}