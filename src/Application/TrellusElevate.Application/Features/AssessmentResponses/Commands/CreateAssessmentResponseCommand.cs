using TrellusElevate.Application.Features.AssessmentResponses.Mappers;
using TrellusElevate.Application.Features.AssessmentResponses.Models;
using TrellusElevate.Application.Features.Assessments.Helpers;
using TrellusElevate.Application.Features.Assessments.Specifications;
using TrellusElevate.Application.Features.QuestionnaireResponses.Models;
using TrellusElevate.Core.Domains.AssessmentResponses.Entities;
using TrellusElevate.Core.Domains.Assessments;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

namespace TrellusElevate.Application.Features.AssessmentResponses.Commands;

public sealed record CreateAssessmentResponseCommand(
    Guid AssessmentId,
    SaveQuestionnaireResponseDto QuestionnaireResponse) : ICommand<Result<AssessmentResponseDto>>;

public sealed class CreateAssessmentResponseCommandHandler(
    IRepository<Assessment> assessmentRepository,
    IRepository<QuestionnaireResponse> questionnaireResponseRepository,
    IRepository<AssessmentResponse> assessmentResponseRepository)
    : ICommandHandler<CreateAssessmentResponseCommand, Result<AssessmentResponseDto>>
{
    public async ValueTask<Result<AssessmentResponseDto>> Handle(CreateAssessmentResponseCommand command,
        CancellationToken cancellationToken)
    {
        var assessment = await assessmentRepository
            .FirstOrDefaultAsync(new AssessmentByIdFullSpec(command.AssessmentId), cancellationToken);
        if (assessment is null) return AssessmentErrors.NotFound(command.AssessmentId);

        var newQuestionnaireResponseResult = QuestionnaireResponse.Create(
            command.QuestionnaireResponse.QuestionnaireId,
            QuestionnaireResponseStatus.Completed);
        if (newQuestionnaireResponseResult.IsError) return newQuestionnaireResponseResult.Error;
        var newQuestionnaireResponse = newQuestionnaireResponseResult.Value;

        var mapItemsResult =
            MapQuestionnaireResponseItems(newQuestionnaireResponse, command.QuestionnaireResponse.Items, null);
        if (mapItemsResult.IsError) return mapItemsResult.Error;

        var newAssessmentResponseResult = AssessmentResponse.Create(command.AssessmentId, newQuestionnaireResponse.Id);
        if (newAssessmentResponseResult.IsError) return newAssessmentResponseResult.Error;
        var newAssessmentResponse = newAssessmentResponseResult.Value;

        foreach (var scoringProfile in assessment.ScoringProfiles.Where(s =>
                     s.Status == AssessmentScoringProfileStatus.Active))
        {
            var scoreCalculationResult =
                ScoreCalculatorHelper.CalculateScore(assessment.Questionnaire!, newQuestionnaireResponse,
                    scoringProfile);

            if (scoreCalculationResult.IsError)
            {
                return scoreCalculationResult.Error;
            }

            var addScoreResult = newAssessmentResponse.AddScore(scoringProfile.Id, scoreCalculationResult.Value);
            if (addScoreResult.IsError)
            {
                return addScoreResult.Error;
            }
        }

        await questionnaireResponseRepository.AddAsync(newQuestionnaireResponse, false, cancellationToken);
        await assessmentResponseRepository.AddAsync(newAssessmentResponse, false, cancellationToken);
        await assessmentResponseRepository.SaveChangesAsync(cancellationToken);

        return newAssessmentResponse.ToDto(newQuestionnaireResponse)!;
    }

    private static Result MapQuestionnaireResponseItems(
        QuestionnaireResponse questionnaireResponse,
        List<SaveQuestionnaireResponseItemDto> items,
        Guid? parentQuestionnaireResponseItemId)
    {
        foreach (var item in items)
        {
            var addResult = questionnaireResponse.AddQuestionnaireResponseItem(
                item.QuestionnaireItemId,
                parentQuestionnaireResponseItemId,
                item.Answers);

            if (addResult.IsError) return addResult.Error;

            if (item.Items.Count <= 0) continue;

            var nestedMapResult = MapQuestionnaireResponseItems(
                questionnaireResponse,
                item.Items,
                addResult.Value.Id);

            if (nestedMapResult.IsError) return nestedMapResult.Error;
        }

        return Result.Ok();
    }
}

