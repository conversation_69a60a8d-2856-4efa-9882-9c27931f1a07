using TrellusElevate.Application.Features.AssessmentResponses.Models;
using TrellusElevate.Application.Features.Assessments.Mappers;
using TrellusElevate.Application.Features.Questionnaires.Mappers;
using TrellusElevate.Core.Domains.AssessmentResponses.Entities;
using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

namespace TrellusElevate.Application.Features.AssessmentResponses.Mappers;

public static class AssessmentResponseMappers
{
    public static AssessmentResponseDto? ToDto(this AssessmentResponse? entity,
        QuestionnaireResponse? questionnaireResponse = null) =>
        entity is null
            ? null
            : new AssessmentResponseDto
            {
                Id = entity.Id,
                QuestionnaireResponse = (entity.QuestionnaireResponse ?? questionnaireResponse!).ToDto(),
                Scores = entity.Scores.Select(s => s.ToDto()).ToList()
            };

    public static AssessmentResponseScoreDto ToDto(this AssessmentResponseScore entity) =>
        new AssessmentResponseScoreDto
        {
            Id = entity.Id,
            AssessmentScoringProfileId = entity.AssessmentScoringProfileId,
            AssessmentScoringProfile = entity.AssessmentScoringProfile?.ToDto(),
            Value = entity.Value
        };
}