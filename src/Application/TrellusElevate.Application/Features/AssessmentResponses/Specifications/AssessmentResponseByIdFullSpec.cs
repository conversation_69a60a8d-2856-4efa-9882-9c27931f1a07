using TrellusElevate.Core.Domains.AssessmentResponses.Entities;

namespace TrellusElevate.Application.Features.AssessmentResponses.Specifications;

public sealed class AssessmentResponseByIdFullSpec : Specification<AssessmentResponse>
{
    public AssessmentResponseByIdFullSpec(Guid id, bool asNoTracking = false)
    {
        Query.Where(a => a.Id == id)
            .Include(s => s.QuestionnaireResponse)
            .Include(a => a.Scores)
            .ThenInclude(s => s.AssessmentScoringProfile);

        if (asNoTracking) Query.AsNoTracking();
    }
}