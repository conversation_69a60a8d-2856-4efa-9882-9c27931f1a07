using NCalc;
using System.Text.RegularExpressions;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;
using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.Common.Helpers;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Application.Features.Assessments.Helpers;

public static partial class ScoreCalculatorHelper
{
    public static Result<double> CalculateScore(Questionnaire questionnaire,
        QuestionnaireResponse questionnaireResponse, AssessmentScoringProfile scoringProfile)
    {
        if (string.IsNullOrWhiteSpace(scoringProfile.CalculationFormula))
        {
            return 0;
        }

        try
        {
            var expression = new Expression(scoringProfile.CalculationFormula);

            // Extract all parameter names from the formula
            var formulaParameters = ExtractParametersFromFormula(scoringProfile.CalculationFormula);
            var missingParameters = new List<string>();
            var processedParameters = new HashSet<string>();

            // Process each response item and set parameter values
            foreach (var responseItem in questionnaireResponse.Items)
            {
                var questionnaireItem =
                    questionnaire.Items.FirstOrDefault(ri => ri.Id == responseItem.QuestionnaireItemId);

                if (questionnaireItem is null || !questionnaireItem.Type.IsAnswerableType())
                    continue;

                string parameterName = questionnaireItem.LinkId;
                processedParameters.Add(parameterName);

                // Skip if no answers provided
                if (responseItem.Answers.Count <= 0)
                {
                    // Set default value for questions without answers if referenced in formula
                    if (formulaParameters.Contains(parameterName))
                    {
                        expression.Parameters[parameterName] = 0.0;
                    }
                    continue;
                }

                var responseAnswers = responseItem.Answers;

                // Find the best matching score mapping for this response
                var matchedMapping = FindBestMatchingScoreMapping(scoringProfile.ScoreMappings, questionnaireItem.Id, responseAnswers);

                if (matchedMapping != null)
                {
                    expression.Parameters[parameterName] = matchedMapping.Weight;
                }
                else if (formulaParameters.Contains(parameterName))
                {
                    // Set default value for answered questions without score mappings
                    expression.Parameters[parameterName] = 0.0;
                }
            }

            // Check for parameters in formula that don't have corresponding questionnaire items
            foreach (var formulaParam in formulaParameters)
            {
                if (processedParameters.Contains(formulaParam) ||
                    expression.Parameters.ContainsKey(formulaParam)) continue;
                missingParameters.Add(formulaParam);
                // Set default value to prevent calculation errors
                expression.Parameters[formulaParam] = 0.0;
            }
            
            var result = expression.Evaluate();

            return result switch
            {
                double doubleResult => doubleResult,
                int intResult => (double)intResult,
                decimal decimalResult => (double)decimalResult,
                float floatResult => (double)floatResult,
                long longResult => (double)longResult,
                _ => Error.Failure("AssessmentScore.InvalidCalculationValue",
                    $"Invalid calculation value: {result} (Type: {result?.GetType().Name ?? "null"})")
            };
        }
        catch (Exception ex)
        {
            return Error.Failure("AssessmentScore.ErrorOnCalculation",
                $"Error on calculation: {ex.Message}. Formula: {scoringProfile.CalculationFormula}");
        }
    }

    /// <summary>
    /// Extracts parameter names from a calculation formula using regex
    /// </summary>
    /// <param name="formula">The calculation formula</param>
    /// <returns>Set of parameter names found in the formula</returns>
    private static HashSet<string> ExtractParametersFromFormula(string formula)
    {
        if (string.IsNullOrWhiteSpace(formula)) return [];

        // Regex to match parameter names (alphanumeric + underscore, not starting with digit)
        var parameterRegex = MyRegex();
        var matches = parameterRegex.Matches(formula);

        var parameters = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        // Common mathematical functions and operators to exclude
        var excludedTerms = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "abs", "acos", "asin", "atan", "ceiling", "cos", "exp", "floor",
            "log", "log10", "max", "min", "pow", "round", "sign", "sin",
            "sqrt", "tan", "truncate", "if", "in", "true", "false", "null",
            "and", "or", "not"
        };

        foreach (Match match in matches)
        {
            var parameter = match.Value;
            if (!excludedTerms.Contains(parameter) && !IsNumeric(parameter))
            {
                parameters.Add(parameter);
            }
        }

        return parameters;
    }

    /// <summary>
    /// Checks if a string represents a numeric value
    /// </summary>
    private static bool IsNumeric(string value)
    {
        return double.TryParse(value, out _);
    }

    /// <summary>
    /// Validates a calculation formula and returns any issues found
    /// </summary>
    /// <param name="formula">The formula to validate</param>
    /// <param name="availableParameters">Available parameter names</param>
    /// <returns>Validation result with any errors found</returns>
    public static Result ValidateFormula(string formula, IEnumerable<string> availableParameters)
    {
        if (string.IsNullOrWhiteSpace(formula))
        {
            return Result.Ok();
        }

        try
        {
            var expression = new Expression(formula);

            // Check for syntax errors
            if (expression.HasErrors())
            {
                return Error.Validation("AssessmentScore.InvalidFormulaSyntax",
                    $"Formula has syntax errors: {string.Join(", ", expression.Error)}");
            }

            // Check for undefined parameters
            var formulaParameters = ExtractParametersFromFormula(formula);
            var availableParamSet = new HashSet<string>(availableParameters, StringComparer.OrdinalIgnoreCase);
            var undefinedParameters = formulaParameters.Where(p => !availableParamSet.Contains(p)).ToList();

            if (undefinedParameters.Count > 0)
            {
                return Error.Validation("AssessmentScore.UndefinedParameters",
                    $"Formula references undefined parameters: {string.Join(", ", undefinedParameters)}");
            }

            return Result.Ok();
        }
        catch (Exception ex)
        {
            return Error.Validation("AssessmentScore.FormulaValidationError",
                $"Error validating formula: {ex.Message}");
        }
    }

    /// <summary>
    /// Finds the best matching score mapping for the given response answers.
    /// </summary>
    private static AssessmentScoreMapping? FindBestMatchingScoreMapping(
        IReadOnlyCollection<AssessmentScoreMapping> scoreMappings,
        Guid questionnaireItemId,
        List<QuestionAnswer> responseAnswers)
    {
        var candidateMappings = scoreMappings
            .Where(mapping => mapping.QuestionnaireItemId == questionnaireItemId)
            .ToList();

        if (!candidateMappings.Any()) return null;

        // Use the multiselect logic for all cases
        return candidateMappings.FirstOrDefault(mapping =>
            QuestionAnswer.IsSatisfiedBy(responseAnswers, mapping.Answers, mapping.Operator));
    }

    [GeneratedRegex(@"\b[a-zA-Z_][a-zA-Z0-9_]*\b", RegexOptions.Compiled)]
    private static partial Regex MyRegex();
}