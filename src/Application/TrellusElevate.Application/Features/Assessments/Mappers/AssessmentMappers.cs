using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Application.Features.Questionnaires.Mappers;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Assessments.Mappers;

public static class AssessmentMappers
{
    public static AssessmentDto? ToDto(this Assessment? entity, Questionnaire? questionnaire = null) =>
        entity is null
            ? null
            : new AssessmentDto
            {
                Id = entity.Id,
                Name = entity.Name,
                Description = entity.Description,
                QuestionnaireId = entity.QuestionnaireId,
                Questionnaire = (entity.Questionnaire ?? questionnaire)?.ToDto(),
                Status = entity.Status,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt,
                AssessmentScoringProfiles = entity.ScoringProfiles.Select(s => s.ToDto()!).ToList()
            };

    public static AssessmentScoringProfileDto? ToDto(this AssessmentScoringProfile? entity) =>
        entity is null
            ? null
            : new AssessmentScoringProfileDto
            {
                Id = entity.Id,
                Name = entity.Name,
                Description = entity.Description,
                Status = entity.Status,
                CalculationFormula = entity.CalculationFormula,
                AssessmentScoreMappings = entity.ScoreMappings
                    .Select(s => s.ToDto()!).ToList()
            };

    public static AssessmentScoreMappingDto? ToDto(this AssessmentScoreMapping? entity, QuestionnaireItem? questionnaireItem = null) =>
        entity is null
            ? null
            : new AssessmentScoreMappingDto
            {
                Id = entity.Id,
                QuestionnaireItemId = entity.QuestionnaireItemId,
                QuestionnaireItem = questionnaireItem?.ToDto(),
                Answers = entity.Answers,
                Operator = entity.Operator,
                Weight = entity.Weight,
            };
}