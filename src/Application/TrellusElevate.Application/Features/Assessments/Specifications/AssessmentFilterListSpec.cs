using TrellusElevate.Application.Features.Assessments.Enums;
using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Application.Features.Assessments.Specifications;

public sealed class AssessmentFilterListSpec : Specification<Assessment, AssessmentDto>
{
    public AssessmentFilterListSpec(string? searchTerm,
        AssessmentSort? sortBy,
        SortDirection orderDirection,
        int page,
        int pageSize,
        List<AssessmentStatus>? status)
    {
        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(p =>
                p.Name.Contains(searchTerm) ||
                p.Description!.Contains(searchTerm));
        }

        if (status != null && status.Count != 0)
        {
            Query.Where(p => status.Contains(p.Status));
        }

        sortBy ??= AssessmentSort.CreatedAt;

        switch (sortBy)
        {
            case AssessmentSort.Status:
                Query.ApplyOrdering(s => s.Status, orderDirection);
                break;
            case AssessmentSort.Name:
                Query.ApplyOrdering(s => s.Name, orderDirection);
                break;
            case AssessmentSort.UpdatedAt:
                Query.ApplyOrdering(s => s.UpdatedAt, orderDirection);
                break;
            case AssessmentSort.CreatedAt:
                Query.ApplyOrdering(s => s.CreatedAt, orderDirection);
                break;
            default:
                Query.ApplyOrdering(s => s.Id, orderDirection);
                break;
        }

        Query.Skip((page - 1) * pageSize).Take(pageSize);

        Query.Select(s => new AssessmentDto
        {
            Id = s.Id,
            Name = s.Name,
            Status = s.Status,
            CreatedAt = s.CreatedAt,
            UpdatedAt = s.UpdatedAt
        });
    }
}