using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Application.Features.Assessments.Specifications;

public sealed class AssessmentByIdFullSpec : Specification<Assessment>
{
    public AssessmentByIdFullSpec(Guid id, bool asNoTracking = false)
    {
        Query.Where(a => a.Id == id)
            .Include(s => s.Questionnaire)
            .Include(a => a.ScoringProfiles)
            .ThenInclude(p => p.ScoreMappings);

        if (asNoTracking) Query.AsNoTracking();
    }
}