using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Application.Features.Assessments.Models;

public sealed class AssessmentScoringProfileDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public string? CalculationFormula { get; set; }
    public AssessmentScoringProfileStatus Status { get; set; }
    public List<AssessmentScoreMappingDto> AssessmentScoreMappings { get; set; } = [];
}