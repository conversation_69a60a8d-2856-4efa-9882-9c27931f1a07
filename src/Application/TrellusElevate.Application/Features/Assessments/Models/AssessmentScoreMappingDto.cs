using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Application.Features.Assessments.Models;

public sealed class AssessmentScoreMappingDto
{
    public Guid Id { get; set; }

    public Guid QuestionnaireItemId { get; set; }
    public QuestionnaireItemDto? QuestionnaireItem { get; set; }

    public List<QuestionAnswer> Answers { get; set; } = null!;
    public Operator? Operator { get; set; }
    public double? Weight { get; set; }
}