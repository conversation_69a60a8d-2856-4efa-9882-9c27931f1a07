using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Application.Features.Assessments.Models;

public sealed class AssessmentDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string? Description { get; set; }

    public Guid QuestionnaireId { get; set; }
    public QuestionnaireDto? Questionnaire { get; set; }

    public AssessmentStatus Status { get; set; }

    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset? UpdatedAt { get; set; }

    public List<AssessmentScoringProfileDto>? AssessmentScoringProfiles { get; set; }
}