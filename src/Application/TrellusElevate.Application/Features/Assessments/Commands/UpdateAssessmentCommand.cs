using TrellusElevate.Application.Features.Assessments.Mappers;
using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Application.Features.Assessments.Specifications;
using TrellusElevate.Core.Domains.Assessments;
using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Application.Features.Assessments.Commands;

public sealed record UpdateAssessmentCommand(
    Guid Id,
    string Name,
    string? Description,
    Guid QuestionnaireId,
    AssessmentStatus Status) : ICommand<Result<AssessmentDto>>;

public sealed class UpdateAssessmentCommandHandler(IRepository<Assessment> repository)
    : ICommandHandler<UpdateAssessmentCommand, Result<AssessmentDto>>
{
    public async ValueTask<Result<AssessmentDto>> Handle(UpdateAssessmentCommand command,
        CancellationToken cancellationToken)
    {
        var assessment = await repository
            .FirstOrDefaultAsync(new AssessmentByIdFullSpec(command.Id), cancellationToken);

        if (assessment is null) return AssessmentErrors.NotFound(command.Id);

        if (assessment.QuestionnaireId != command.QuestionnaireId)
        {
            assessment.UpdateQuestionnaire(command.QuestionnaireId);
        }

        var result = assessment.Update(command.Name, command.Description!, command.Status);

        if (result.IsError) return result.Error;

        await repository.UpdateAsync(assessment, cancellationToken);

        return assessment.ToDto()!;
    }
}