using TrellusElevate.Application.Features.Assessments.Helpers;
using TrellusElevate.Application.Features.Assessments.Mappers;
using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Application.Features.Assessments.Specifications;
using TrellusElevate.Core.Domains.Assessments;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Questionnaires.Common.Helpers;

namespace TrellusElevate.Application.Features.Assessments.Commands;

public sealed record UpdateAssessmentScoringProfileCommand(
    Guid AssessmentId,
    Guid ScoringProfileId,
    string Name,
    string? Description,
    string? CalculationFormula,
    AssessmentScoringProfileStatus Status) : ICommand<Result<AssessmentScoringProfileDto>>;

public sealed class UpdateAssessmentScoringProfileCommandHandler(IRepository<Assessment> repository)
    : ICommandHandler<UpdateAssessmentScoringProfileCommand, Result<AssessmentScoringProfileDto>>
{
    public async ValueTask<Result<AssessmentScoringProfileDto>> Handle(UpdateAssessmentScoringProfileCommand command,
        CancellationToken cancellationToken)
    {
        var assessment = await repository
            .FirstOrDefaultAsync(new AssessmentByIdFullSpec(command.AssessmentId), cancellationToken);

        if (assessment is null) return AssessmentErrors.NotFound(command.AssessmentId);

        // Validate calculation formula if provided
        if (!string.IsNullOrWhiteSpace(command.CalculationFormula))
        {
            var availableParameters = assessment.Questionnaire?.Items
                .Where(item => item.Type.IsAnswerableType())
                .Select(item => item.LinkId) ?? [];

            var formulaValidation = ScoreCalculatorHelper.ValidateFormula(command.CalculationFormula, availableParameters);
            if (formulaValidation.IsError) return formulaValidation.Error;
        }

        var result = assessment.UpdateScoringProfile(
            command.ScoringProfileId,
            command.Name,
            command.Description,
            command.CalculationFormula,
            command.Status);

        if (result.IsError) return result.Error;

        await repository.UpdateAsync(assessment, cancellationToken);

        var updatedProfile = assessment.ScoringProfiles.FirstOrDefault(p => p.Id == command.ScoringProfileId);
        return updatedProfile!.ToDto()!;
    }
}