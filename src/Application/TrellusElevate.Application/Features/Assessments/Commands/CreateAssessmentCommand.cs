using TrellusElevate.Application.Features.Assessments.Mappers;
using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Questionnaires;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Assessments.Commands;

public sealed record CreateAssessmentCommand(
    string Name,
    string? Description,
    Guid QuestionnaireId) : ICommand<Result<AssessmentDto>>;

public sealed class CreateAssessmentCommandHandler(
    IRepository<Questionnaire> questionnaireRepository,
    IRepository<Assessment> assessmentRepository)
    : ICommandHandler<CreateAssessmentCommand, Result<AssessmentDto>>
{
    public async ValueTask<Result<AssessmentDto>> Handle(CreateAssessmentCommand command,
        CancellationToken cancellationToken)
    {
        var questionnaire = await questionnaireRepository.GetByIdAsync(command.QuestionnaireId, cancellationToken);

        if (questionnaire is null) return QuestionnaireErrors.NotFound(command.QuestionnaireId);

        var entityResult = Assessment.Create(command.Name, command.Description, command.QuestionnaireId);

        if (entityResult.IsError) return entityResult.Error;

        var result = await assessmentRepository.AddAsync(entityResult.Value, cancellationToken);

        return result.ToDto(questionnaire)!;
    }
}