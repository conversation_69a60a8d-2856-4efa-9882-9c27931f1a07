using TrellusElevate.Application.Features.Assessments.Mappers;
using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Application.Features.Assessments.Specifications;
using TrellusElevate.Core.Domains.Assessments;
using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Application.Features.Assessments.Commands;

public sealed record CreateAssessmentScoringProfileCommand(
    Guid AssessmentId,
    string Name,
    string? Description
) : ICommand<Result<AssessmentScoringProfileDto>>;

public sealed class CreateAssessmentScoringProfileCommandHandler(IRepository<Assessment> repository)
    : ICommandHandler<CreateAssessmentScoringProfileCommand, Result<AssessmentScoringProfileDto>>
{
    public async ValueTask<Result<AssessmentScoringProfileDto>> Handle(CreateAssessmentScoringProfileCommand command,
        CancellationToken cancellationToken)
    {
        var assessment = await repository.FirstOrDefaultAsync(new AssessmentByIdFullSpec(command.AssessmentId) , cancellationToken);

        if (assessment is null) return AssessmentErrors.NotFound(command.AssessmentId);

        var result = assessment.AddScoringProfile(command.Name, command.Description);

        if (result.IsError) return result.Error;

        await repository.UpdateAsync(assessment, cancellationToken);

        return result.Value.ToDto()!;
    }
}