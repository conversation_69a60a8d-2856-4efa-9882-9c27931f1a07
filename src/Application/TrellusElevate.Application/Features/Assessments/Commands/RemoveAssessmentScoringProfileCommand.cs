using TrellusElevate.Application.Features.Assessments.Specifications;
using TrellusElevate.Core.Domains.Assessments;
using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Application.Features.Assessments.Commands;

public sealed record RemoveAssessmentScoringProfileCommand(
    Guid AssessmentId,
    Guid ScoringProfileId) : ICommand<Result>;

internal sealed class RemoveAssessmentScoringProfileCommandHandler(IRepository<Assessment> repository)
    : ICommandHandler<RemoveAssessmentScoringProfileCommand, Result>
{
    public async ValueTask<Result> Handle(RemoveAssessmentScoringProfileCommand request,
        CancellationToken cancellationToken)
    {
        var assessment = await repository.FirstOrDefaultAsync(new AssessmentByIdFullSpec(request.AssessmentId) , cancellationToken);

        if (assessment is null) return AssessmentErrors.NotFound(request.AssessmentId);

        var result = assessment.RemoveScoringProfile(request.ScoringProfileId);

        if (result.IsError) return result.Error;

        await repository.UpdateAsync(assessment, cancellationToken);

        return Result.Ok();
    }
}