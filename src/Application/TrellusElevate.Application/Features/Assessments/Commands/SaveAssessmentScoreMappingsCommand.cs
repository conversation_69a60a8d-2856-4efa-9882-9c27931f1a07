using TrellusElevate.Application.Features.Assessments.Mappers;
using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Application.Features.Assessments.Specifications;
using TrellusElevate.Core.Domains.Assessments;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Questionnaires;

namespace TrellusElevate.Application.Features.Assessments.Commands;

public sealed record SaveAssessmentScoreMappingsCommand(
    Guid AssessmentId,
    Guid ScoringProfileId,
    List<AssessmentScoreMappingDto> Mappings) : ICommand<Result<IReadOnlyList<AssessmentScoreMappingDto>>>;

public sealed class SaveAssessmentScoreMappingsCommandHandler(IRepository<Assessment> repository)
    : ICommandHandler<SaveAssessmentScoreMappingsCommand, Result<IReadOnlyList<AssessmentScoreMappingDto>>>
{
    public async ValueTask<Result<IReadOnlyList<AssessmentScoreMappingDto>>> Handle(
        SaveAssessmentScoreMappingsCommand command,
        CancellationToken cancellationToken)
    {
        var assessment = await repository
            .FirstOrDefaultAsync(new AssessmentByIdFullSpec(command.AssessmentId), cancellationToken);

        if (assessment is null) return AssessmentErrors.NotFound(command.AssessmentId);

        // Find the scoring profile
        var scoringProfile = assessment.ScoringProfiles
            .FirstOrDefault(p => p.Id == command.ScoringProfileId);

        if (scoringProfile is null) return AssessmentErrors.ScoringProfileNotFound(command.ScoringProfileId);

        // Get current mappings for the profile
        var existingMappings = scoringProfile.ScoreMappings.ToDictionary(m => m.Id);

        // Load all questionnaire items for validation
        var questionnaireItemIds = command.Mappings.Select(m => m.QuestionnaireItemId).Distinct().ToList();
        var questionnaireItems = assessment.Questionnaire!.Items
            .Where(i => questionnaireItemIds.Contains(i.Id))
            .ToDictionary(i => i.Id);

        // Check if all questionnaire items exist
        foreach (var mapping in command.Mappings)
        {
            if (!questionnaireItems.ContainsKey(mapping.QuestionnaireItemId))
            {
                return QuestionnaireErrors.QuestionnaireItemNotFound(mapping.QuestionnaireItemId);
            }
        }

        // Process mappings in transaction
        var updatedMappings = new List<AssessmentScoreMapping>();

        // Identify mappings to process: mappings to update, add, and remove
        var mappingsToUpdate = command.Mappings
            .Where(m => m.Id != Guid.Empty && existingMappings.ContainsKey(m.Id))
            .ToList();
        var mappingsToAdd = command.Mappings
            .Where(m => m.Id == Guid.Empty)
            .ToList();
        var mappingIdsToKeep = mappingsToUpdate
            .Where(m => m.Id != Guid.Empty)
            .Select(m => m.Id)
            .ToHashSet();
        var mappingsToRemove = existingMappings.Values
            .Where(m => !mappingIdsToKeep.Contains(m.Id))
            .ToList();

        // Process the updates
        foreach (var mappingDto in mappingsToUpdate)
        {
            var result = assessment.UpdateScoreMapping(
                command.ScoringProfileId,
                mappingDto.Id,
                mappingDto.Answers,
                mappingDto.Operator!.Value,
                mappingDto.Weight!.Value);

            if (result.IsError) return result.Error;

            var updatedMapping = existingMappings[mappingDto.Id];
            updatedMappings.Add(updatedMapping);
        }

        // Process the additions
        foreach (var mappingDto in mappingsToAdd)
        {
            var result = assessment.AddScoreMapping(
                command.ScoringProfileId,
                mappingDto.QuestionnaireItemId,
                mappingDto.Answers,
                mappingDto.Operator!.Value,
                mappingDto.Weight!.Value);

            if (result.IsError) return result.Error;

            updatedMappings.Add(result.Value);
        }

        // Process the removals
        foreach (var mapping in mappingsToRemove)
        {
            var result = assessment.RemoveScoreMapping(command.ScoringProfileId, mapping.Id);

            if (result.IsError) return result.Error;
        }

        await repository.UpdateAsync(assessment, cancellationToken);

        return updatedMappings.Select(mapping => mapping.ToDto(questionnaireItems[mapping.QuestionnaireItemId])!)
            .ToList();
    }
}