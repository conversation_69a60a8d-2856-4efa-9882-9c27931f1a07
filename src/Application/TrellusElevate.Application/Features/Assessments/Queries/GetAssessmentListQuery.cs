using TrellusElevate.Application.Features.Assessments.Enums;
using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Application.Features.Assessments.Specifications;
using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Application.Features.Assessments.Queries;

public sealed record GetAssessmentListQuery : PaginatedFilterQuery<AssessmentSort>,
    IQuery<Result<PaginatedResultDto<AssessmentDto>>>
{
    public IEnumerable<AssessmentStatus>? Status { get; set; }
}

public sealed class GetAssessmentListQueryHandler(IRepository<Assessment> repository)
    : IQueryHandler<GetAssessmentListQuery, Result<PaginatedResultDto<AssessmentDto>>>
{
    public async ValueTask<Result<PaginatedResultDto<AssessmentDto>>> Handle(GetAssessmentListQuery query,
        CancellationToken cancellationToken)
    {
        var totalCount = await repository.CountAsync(new AssessmentFilterListSpec(
            query.SearchTerm,
            null,
            SortDirection.Asc,
            1,
            int.MaxValue,
            query.Status?.ToList()
        ), cancellationToken);

        var items = await repository.ListAsync(new AssessmentFilterListSpec(
            query.SearchTerm,
            query.SortBy,
            query.SortDirection,
            query.Page,
            query.PageSize,
            query.Status?.ToList()
        ), cancellationToken);

        return new PaginatedResultDto<AssessmentDto>(query.Page, query.PageSize, totalCount, items);
    }
}