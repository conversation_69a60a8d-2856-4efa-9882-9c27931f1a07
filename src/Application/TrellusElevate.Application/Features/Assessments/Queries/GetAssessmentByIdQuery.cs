using TrellusElevate.Application.Features.Assessments.Mappers;
using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Application.Features.Assessments.Specifications;
using TrellusElevate.Core.Domains.Assessments;
using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Application.Features.Assessments.Queries;

public sealed record GetAssessmentByIdQuery(Guid Id) : IQuery<Result<AssessmentDto>>;

public sealed class GetAssessmentByIdQueryHandler(IReadRepository<Assessment> repository)
    : IQueryHandler<GetAssessmentByIdQuery, Result<AssessmentDto>>
{
    public async ValueTask<Result<AssessmentDto>> Handle(GetAssessmentByIdQuery query,
        CancellationToken cancellationToken)
    {
        var assessment = await repository
            .FirstOrDefaultAsync(new AssessmentByIdFullSpec(query.Id, true), cancellationToken);

        if (assessment is null) return AssessmentErrors.NotFound(query.Id);

        return assessment.ToDto()!;
    }
}