using TrellusElevate.Application.Features.QuestionnaireResponses.Models;
using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Core.Domains.Questionnaires.Common.Helpers;

namespace TrellusElevate.Application.Features.QuestionnaireResponses.Mappers;

public static class QuestionnaireResponseMappers
{
    public static SaveQuestionnaireResponseItemDto ToSaveResponseDto(this QuestionnaireItemDto dto)
    {
        var initial =
            (dto.Options.Count != 0 ? dto.Options.Where(s => s.InitialSelected)
                    .Select(s => s.Answer!) :
                dto.Initial != null ? [dto.Initial] : []).ToList();
        return new SaveQuestionnaireResponseItemDto
        {
            QuestionnaireItemId = dto.Id,
            Answers = dto.Type.IsAnswerableType() ? initial : [],
            Items = dto.Items.Select(ToSaveResponseDto).ToList(),
        };
    }
}