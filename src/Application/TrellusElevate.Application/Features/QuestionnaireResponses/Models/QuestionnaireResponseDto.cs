using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

namespace TrellusElevate.Application.Features.QuestionnaireResponses.Models;

public sealed class QuestionnaireResponseDto
{
    public Guid Id { get; set; }
    public Guid QuestionnaireId { get; set; }
    public QuestionnaireResponseStatus Status { get; set; }
    public DateTimeOffset? CreatedAt { get; set; }
    public List<QuestionnaireResponseItemDto> Items { get; set; } = null!;
}