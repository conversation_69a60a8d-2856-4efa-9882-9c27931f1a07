using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Application.Features.QuestionnaireResponses.Models;

public sealed class QuestionnaireResponseItemDto
{
    public Guid Id { get; set; }
    public Guid QuestionnaireItemId { get; set; }
    public string? LinkId { get; set; }
    public string? Text { get; set; }
    public int OrderNumber { get; set; }
    public List<QuestionAnswer> Answers { get; set; } = null!;
    public List<QuestionnaireResponseItemDto> Items { get; set; } = null!;
}