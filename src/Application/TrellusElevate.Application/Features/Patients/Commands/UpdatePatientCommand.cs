using TrellusElevate.Application.Common.Attributes;
using TrellusElevate.Application.Features.Patients.Mappers;
using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Core.Common.Enums;
using TrellusElevate.Core.Domains.Patients;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Commands;

public sealed record UpdatePatientCommand(
    Guid Id,
    [property: MaskName] string FirstName,
    [property: MaskName] string MiddleName,
    [property: MaskName] string LastName,
    [property: MaskDate] DateOnly BirthDate,
    Gender Gender,
    [property: MaskEmail] string ContactEmail,
    [property: MaskPhone] string ContactPhone
) : ICommand<Result<PatientDto>>;

internal sealed class UpdatePatientCommandHandler(IRepository<Patient> repository)
    : ICommandHandler<UpdatePatientCommand, Result<PatientDto>>
{
    public async ValueTask<Result<PatientDto>> Handle(UpdatePatientCommand command, CancellationToken cancellationToken)
    {
        var patient = await repository.GetByIdAsync(command.Id, cancellationToken);
        if (patient is null) return PatientErrors.NotFound(command.Id);

        var result = patient.Update(command.FirstName, command.MiddleName, command.LastName, command.BirthDate,
            command.Gender,
            command.ContactEmail, command.ContactPhone);

        if (result.IsError) return result.Error;

        await repository.UpdateAsync(patient, cancellationToken);

        return patient.ToDto()!;
    }
}