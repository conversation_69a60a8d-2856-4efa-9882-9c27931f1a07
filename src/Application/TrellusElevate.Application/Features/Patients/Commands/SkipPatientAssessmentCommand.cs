using TrellusElevate.Application.Features.Patients.Mappers;
using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Application.Features.Patients.Specifications;
using TrellusElevate.Core.Domains.Patients;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Commands;

public sealed record SkipPatientAssessmentCommand(
    Guid PatientId,
    Guid PatientAssessmentId) : ICommand<Result<PatientAssessmentDto>>;

public sealed class SkipPatientAssessmentCommandHandler(
    IRepository<Patient> patientRepository)
    : ICommandHandler<SkipPatientAssessmentCommand, Result<PatientAssessmentDto>>
{
    public async ValueTask<Result<PatientAssessmentDto>> Handle(SkipPatientAssessmentCommand command,
        CancellationToken cancellationToken)
    {
        var patient = await patientRepository
            .FirstOrDefaultAsync(new PatientWithAssessmentSpec(command.PatientId), cancellationToken);
        if (patient is null) return PatientErrors.NotFound(command.PatientId);

        var patientAssessment = patient.Assessments.FirstOrDefault(a => a.Id == command.PatientAssessmentId);
        if (patientAssessment is null) return PatientErrors.PatientAssessmentNotFound(command.PatientAssessmentId);

        var skipResult = patient.SkipPatientAssessment(command.PatientAssessmentId);
        if (skipResult.IsError) return skipResult.Error;

        await patientRepository.UpdateAsync(patient, cancellationToken);

        return patientAssessment.ToDto()!;
    }
}
