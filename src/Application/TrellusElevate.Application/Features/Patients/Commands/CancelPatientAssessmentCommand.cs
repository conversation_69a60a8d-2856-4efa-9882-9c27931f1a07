using TrellusElevate.Application.Features.Patients.Mappers;
using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Application.Features.Patients.Specifications;
using TrellusElevate.Core.Domains.Patients;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Commands;

public sealed record CancelPatientAssessmentCommand(
    Guid PatientId,
    Guid PatientAssessmentId) : ICommand<Result<PatientAssessmentDto>>;

public sealed class CancelPatientAssessmentCommandHandler(
    IRepository<Patient> patientRepository)
    : ICommandHandler<CancelPatientAssessmentCommand, Result<PatientAssessmentDto>>
{
    public async ValueTask<Result<PatientAssessmentDto>> Handle(CancelPatientAssessmentCommand command,
        CancellationToken cancellationToken)
    {
        var patient = await patientRepository
            .FirstOrDefaultAsync(new PatientWithAssessmentSpec(command.PatientId), cancellationToken);
        if (patient is null) return PatientErrors.NotFound(command.PatientId);

        var patientAssessment = patient.Assessments.FirstOrDefault(a => a.Id == command.PatientAssessmentId);
        if (patientAssessment is null) return PatientErrors.PatientAssessmentNotFound(command.PatientAssessmentId);

        var cancelResult = patient.CancelAssessment(command.PatientAssessmentId);
        if (cancelResult.IsError) return cancelResult.Error;

        await patientRepository.UpdateAsync(patient, cancellationToken);

        return patientAssessment.ToDto()!;
    }
}
