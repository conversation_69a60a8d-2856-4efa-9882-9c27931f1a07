using TrellusElevate.Application.Features.Patients.Mappers;
using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Application.Features.Patients.Specifications;
using TrellusElevate.Core.Domains.Assessments;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Patients;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Commands;

public sealed record CreatePatientAssessmentCommand(
    Guid PatientId,
    Guid AssessmentId) : ICommand<Result<PatientAssessmentListItemDto>>;

public sealed class CreatePatientAssessmentCommandHandler(
    IRepository<Patient> patientRepository,
    IRepository<Assessment> assessmentRepository)
    : ICommandHandler<CreatePatientAssessmentCommand, Result<PatientAssessmentListItemDto>>
{
    public async ValueTask<Result<PatientAssessmentListItemDto>> Handle(CreatePatientAssessmentCommand command,
        CancellationToken cancellationToken)
    {
        var patient = await patientRepository
            .FirstOrDefaultAsync(new PatientWithAssessmentSpec(command.PatientId), cancellationToken);
        if (patient is null) return PatientErrors.NotFound(command.PatientId);

        var assessment = await assessmentRepository.GetByIdAsync(command.AssessmentId, cancellationToken);
        if (assessment is null) return AssessmentErrors.NotFound(command.AssessmentId);

        var result = patient.AddAssessment(command.AssessmentId);
        if (result.IsError) return result.Error;

        await patientRepository.UpdateAsync(patient, cancellationToken);

        return result.Value.ToLightDto(assessment)!;
    }
}