using TrellusElevate.Application.Common.Attributes;
using TrellusElevate.Application.Features.Patients.Mappers;
using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Core.Common.Enums;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Commands;

public sealed record CreatePatientCommand(
    [property: MaskName] string FirstName,
    [property: MaskName] string MiddleName,
    [property: MaskName] string LastName,
    [property: MaskDate] DateOnly BirthDate,
    Gender Gender,
    PatientStatus Status,
    [property: MaskEmail] string ContactEmail,
    [property: MaskPhone] string ContactPhone
) : ICommand<Result<PatientDto>>;

internal sealed class CreatePatientCommandHandler(IRepository<Patient> repository)
    : ICommandHandler<CreatePatientCommand, Result<PatientDto>>
{
    public async ValueTask<Result<PatientDto>> Handle(CreatePatientCommand command, CancellationToken cancellationToken)
    {
        var patientResult = Patient.Create(command.FirstName, command.MiddleName, command.LastName, command.BirthDate,
            command.Gender, command.Status, command.ContactEmail, command.ContactPhone);

        if (patientResult.IsError) return patientResult.Error;

        var result = await repository.AddAsync(patientResult.Value, cancellationToken);

        return result.ToDto()!;
    }
}