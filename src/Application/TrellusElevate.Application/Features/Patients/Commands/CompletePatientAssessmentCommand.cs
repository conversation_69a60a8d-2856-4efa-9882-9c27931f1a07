using TrellusElevate.Application.Features.Assessments.Helpers;
using TrellusElevate.Application.Features.Assessments.Specifications;
using TrellusElevate.Application.Features.Patients.Mappers;
using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Application.Features.Patients.Specifications;
using TrellusElevate.Application.Features.QuestionnaireResponses.Models;
using TrellusElevate.Core.Domains.AssessmentResponses.Entities;
using TrellusElevate.Core.Domains.Assessments;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Patients;
using TrellusElevate.Core.Domains.Patients.Entities;
using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

namespace TrellusElevate.Application.Features.Patients.Commands;

public sealed record CompletePatientAssessmentCommand(
    Guid PatientId,
    Guid PatientAssessmentId,
    SaveQuestionnaireResponseDto QuestionnaireResponse) : ICommand<Result<PatientAssessmentDto>>;

public sealed class CompletePatientAssessmentCommandHandler(
    IRepository<Patient> patientRepository,
    IRepository<Assessment> assessmentRepository,
    IRepository<QuestionnaireResponse> questionnaireResponseRepository,
    IRepository<AssessmentResponse> assessmentResponseRepository)
    : ICommandHandler<CompletePatientAssessmentCommand, Result<PatientAssessmentDto>>
{
    public async ValueTask<Result<PatientAssessmentDto>> Handle(CompletePatientAssessmentCommand command,
        CancellationToken cancellationToken)
    {
        var patient = await patientRepository
            .FirstOrDefaultAsync(new PatientWithAssessmentSpec(command.PatientId), cancellationToken);
        if (patient is null) return PatientErrors.NotFound(command.PatientId);

        var patientAssessment = patient.Assessments.FirstOrDefault(a => a.Id == command.PatientAssessmentId);
        if (patientAssessment is null) return PatientErrors.PatientAssessmentNotFound(command.PatientAssessmentId);

        var assessment = await assessmentRepository
            .FirstOrDefaultAsync(new AssessmentByIdFullSpec(patientAssessment.AssessmentId), cancellationToken);
        if (assessment is null) return AssessmentErrors.NotFound(patientAssessment.AssessmentId);

        var newQuestionnaireResponseResult = QuestionnaireResponse.Create(
            command.QuestionnaireResponse.QuestionnaireId,
            QuestionnaireResponseStatus.Completed);
        if (newQuestionnaireResponseResult.IsError) return newQuestionnaireResponseResult.Error;
        var newQuestionnaireResponse = newQuestionnaireResponseResult.Value;

        var mapItemsResult =
            MapQuestionnaireResponseItems(newQuestionnaireResponse, command.QuestionnaireResponse.Items, null);
        if (mapItemsResult.IsError) return mapItemsResult.Error;

        var newAssessmentResponseResult =
            AssessmentResponse.Create(patientAssessment.AssessmentId, newQuestionnaireResponse.Id);
        if (newAssessmentResponseResult.IsError) return newAssessmentResponseResult.Error;
        var newAssessmentResponse = newAssessmentResponseResult.Value;

        foreach (var scoringProfile in assessment.ScoringProfiles.Where(s =>
                     s.Status == AssessmentScoringProfileStatus.Active))
        {
            var scoreCalculationResult = ScoreCalculatorHelper.CalculateScore(
                assessment.Questionnaire!,
                newQuestionnaireResponse,
                scoringProfile);

            if (scoreCalculationResult.IsError)
            {
                return scoreCalculationResult.Error;
            }

            var addScoreResult = newAssessmentResponse.AddScore(scoringProfile.Id, scoreCalculationResult.Value);
            if (addScoreResult.IsError)
            {
                return addScoreResult.Error;
            }
        }

        patient.CompleteAssessment(command.PatientAssessmentId, newAssessmentResponse.Id);

        await questionnaireResponseRepository.AddAsync(newQuestionnaireResponse, false, cancellationToken);
        await assessmentResponseRepository.AddAsync(newAssessmentResponse, false, cancellationToken);

        await patientRepository.UpdateAsync(patient, cancellationToken);

        return patientAssessment.ToDto()!;
    }

    private static Result MapQuestionnaireResponseItems(
        QuestionnaireResponse questionnaireResponse,
        List<SaveQuestionnaireResponseItemDto> items,
        Guid? parentQuestionnaireResponseItemId)
    {
        foreach (var item in items)
        {
            var addResult = questionnaireResponse.AddQuestionnaireResponseItem(
                item.QuestionnaireItemId,
                parentQuestionnaireResponseItemId,
                item.Answers);

            if (addResult.IsError) return addResult.Error;

            if (item.Items.Count <= 0) continue;

            var nestedMapResult = MapQuestionnaireResponseItems(
                questionnaireResponse,
                item.Items,
                addResult.Value.Id);

            if (nestedMapResult.IsError) return nestedMapResult.Error;
        }

        return Result.Ok();
    }
}