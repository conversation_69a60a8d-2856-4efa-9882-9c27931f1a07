using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Specifications;

public sealed class PatientWithAssessmentSpec : Specification<Patient>
{
    public PatientWithAssessmentSpec(Guid patientId, bool asNoTracking = false)
    {
        Query.Where(p => p.Id == patientId)
            .Include(s => s.Assessments)
            .ThenInclude(s => s.Assessment);
        if (asNoTracking) Query.AsNoTracking();
    }
}