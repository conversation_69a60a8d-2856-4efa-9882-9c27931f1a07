using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Specifications;

public sealed class PatientFilterListSpec : Specification<Patient, PatientListItemDto>
{
    public PatientFilterListSpec(string? searchTerm,
        PatientSort? sortBy,
        SortDirection orderDirection,
        int page,
        int pageSize,
        List<PatientStatus>? status)
    {
        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(p =>
                p.FirstName.Contains(searchTerm) ||
                p.LastName.Contains(searchTerm));
        }

        if (status != null && status.Count != 0)
        {
            Query.Where(p => status.Contains(p.Status));
        }

        sortBy ??= PatientSort.CreatedAt;

        switch (sortBy)
        {
            case PatientSort.Status:
                Query.ApplyOrdering(s => s.Status, orderDirection);
                break;
            case PatientSort.FirstName:
                Query.ApplyOrdering(s => s.FirstName, orderDirection);
                break;
            case PatientSort.LastName:
                Query.ApplyOrdering(s => s.LastName, orderDirection);
                break;
            case PatientSort.CreatedAt:
                Query.ApplyOrdering(s => s.CreatedAt, orderDirection);
                break;
            default:
                Query.ApplyOrdering(s => s.Id, orderDirection);
                break;
        }

        Query.Skip((page - 1) * pageSize).Take(pageSize);

        Query.AsNoTracking().Select(s => new PatientListItemDto
        {
            Id = s.Id,
            FirstName = s.FirstName,
            MiddleName = s.MiddleName,
            LastName = s.LastName,
            Status = s.Status,
            CreatedAt = s.CreatedAt.DateTime,
            BirthDate = s.BirthDate.ToDateTime(TimeOnly.MinValue),
            Gender = s.Gender
        });
    }
}