using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Specifications;

public sealed class PatientAssessmentByIdSpec : Specification<Patient, PatientAssessment>
{
    public PatientAssessmentByIdSpec(Guid patientId, Guid patientAssessmentId)
    {
        Query
            .Where(p => p.Id == patientId)
            .Include(p => p.Assessments)
            .SelectMany(p => p.Assessments.Where(a => a.Id == patientAssessmentId));
        Query.AsNoTracking();
    }
}