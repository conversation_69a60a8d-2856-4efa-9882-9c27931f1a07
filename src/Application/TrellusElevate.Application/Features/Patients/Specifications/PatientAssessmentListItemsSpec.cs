using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Specifications;

public sealed class PatientAssessmentListItemsSpec : Specification<Patient>
{
    public PatientAssessmentListItemsSpec(Guid patientId, bool asNoTracking = false)
    {
        Query
            .Where(p => p.Id == patientId)
            .Include(p => p.Assessments)
            .ThenInclude(pa => pa.Assessment)
            .Include(p => p.Assessments)
            .ThenInclude(pa => pa.AssessmentResponse)
            .ThenInclude(ar => ar!.Scores)
            .ThenInclude(s => s.AssessmentScoringProfile)
            .AsSplitQuery();

        if (asNoTracking) Query.AsNoTracking();
    }
}