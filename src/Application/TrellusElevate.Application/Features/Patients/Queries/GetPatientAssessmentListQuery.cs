using TrellusElevate.Application.Features.Patients.Mappers;
using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Application.Features.Patients.Specifications;
using TrellusElevate.Core.Domains.Patients;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Queries;

public sealed record GetPatientAssessmentListQuery(Guid PatientId)
    : IQuery<Result<ListResultDto<PatientAssessmentListItemDto>>>;

public sealed class GetPatientAssessmentListQueryHandler(IReadRepository<Patient> repository)
    : IQueryHandler<GetPatientAssessmentListQuery, Result<ListResultDto<PatientAssessmentListItemDto>>>
{
    public async ValueTask<Result<ListResultDto<PatientAssessmentListItemDto>>> Handle(
        GetPatientAssessmentListQuery query, CancellationToken cancellationToken)
    {
        var patient = await repository
            .FirstOrDefaultAsync(new PatientAssessmentListItemsSpec(query.PatientId, true), cancellationToken);

        if (patient is null) return PatientErrors.NotFound(query.PatientId);

        var result = patient.Assessments
            .OrderByDescending(pa => pa.CreatedAt)
            .Select(pa => pa.ToLightDto()!)
            .ToList();

        return new ListResultDto<PatientAssessmentListItemDto>(result);
    }
}