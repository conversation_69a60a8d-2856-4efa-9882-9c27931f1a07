using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Application.Features.Patients.Specifications;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Queries;

public sealed record GetPatientListQuery : PaginatedFilterQuery<PatientSort>,
    IQuery<Result<PaginatedResultDto<PatientListItemDto>>>
{
    public IEnumerable<PatientStatus>? Status { get; set; }
}

public sealed class GetPatientListQueryHandler(IReadRepository<Patient> repository)
    : IQueryHandler<GetPatientListQuery, Result<PaginatedResultDto<PatientListItemDto>>>
{
    public async ValueTask<Result<PaginatedResultDto<PatientListItemDto>>> Handle(GetPatientListQuery query,
        CancellationToken cancellationToken)
    {
        var totalCount = await repository.CountAsync(new PatientFilterListSpec(
            query.SearchTerm,
            null,
            SortDirection.Asc,
            1,
            int.MaxValue,
            query.Status?.ToList()
        ), cancellationToken);

        var items = await repository.ListAsync(new PatientFilterListSpec(
            query.SearchTerm,
            query.SortBy,
            query.SortDirection,
            query.Page,
            query.PageSize,
            query.Status?.ToList()
        ), cancellationToken);

        return new PaginatedResultDto<PatientListItemDto>(query.Page, query.PageSize, totalCount, items);
    }
}