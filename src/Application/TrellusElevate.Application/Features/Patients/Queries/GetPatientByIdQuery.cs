using TrellusElevate.Application.Features.Patients.Mappers;
using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Core.Domains.Patients;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Queries;

public sealed record GetPatientByIdQuery(Guid Id) : IQuery<Result<PatientDto>>;

public sealed class GetPatientByIdQueryHandler(IReadRepository<Patient> repository)
    : IQueryHandler<GetPatientByIdQuery, Result<PatientDto>>
{
    public async ValueTask<Result<PatientDto>> Handle(GetPatientByIdQuery query, CancellationToken cancellationToken)
    {
        var patient = await repository.GetByIdAsync(query.Id, cancellationToken);
        if (patient is null) return PatientErrors.NotFound(query.Id);
        return patient.ToDto()!;
    }
}