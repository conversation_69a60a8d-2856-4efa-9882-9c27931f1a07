using TrellusElevate.Application.Features.AssessmentResponses.Specifications;
using TrellusElevate.Application.Features.Assessments.Specifications;
using TrellusElevate.Application.Features.Patients.Mappers;
using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Application.Features.Patients.Specifications;
using TrellusElevate.Core.Domains.AssessmentResponses.Entities;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Patients;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Queries;

public sealed record GetPatientAssessmentByIdQuery(Guid PatientId, Guid PatientAssessmentId)
    : IQuery<Result<PatientAssessmentDto>>;

public sealed class GetPatientAssessmentByIdQueryHandler(
    IReadRepository<Patient> repository,
    IReadRepository<Assessment> assessmentRepository,
    IReadRepository<AssessmentResponse> assessmentResponseRepository)
    : I<PERSON>uery<PERSON><PERSON><PERSON><GetPatientAssessmentByIdQuery, Result<PatientAssessmentDto>>
{
    public async ValueTask<Result<PatientAssessmentDto>> Handle(GetPatientAssessmentByIdQuery query,
        CancellationToken cancellationToken)
    {
        var patientAssessment = await repository
            .FirstOrDefaultAsync(new PatientAssessmentByIdSpec(query.PatientId, query.PatientAssessmentId),
                cancellationToken);
        if (patientAssessment is null) return PatientErrors.PatientAssessmentNotFound(query.PatientAssessmentId);

        var assessment = await assessmentRepository
            .FirstOrDefaultAsync(new AssessmentByIdFullSpec(patientAssessment.AssessmentId), cancellationToken);

        AssessmentResponse? assessmentResponse = null;
        if (patientAssessment.AssessmentResponseId.HasValue)
        {
            assessmentResponse = await assessmentResponseRepository.FirstOrDefaultAsync(
                new AssessmentResponseByIdFullSpec(patientAssessment.AssessmentResponseId.Value), cancellationToken);
        }

        return patientAssessment.ToDto(assessment, assessmentResponse)!;
    }
}