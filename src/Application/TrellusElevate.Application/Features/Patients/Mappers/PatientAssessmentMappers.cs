using TrellusElevate.Application.Features.AssessmentResponses.Mappers;
using TrellusElevate.Application.Features.Assessments.Mappers;
using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Core.Domains.AssessmentResponses.Entities;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Mappers;

public static class PatientAssessmentMappers
{
    public static PatientAssessmentDto? ToDto(this PatientAssessment? entity, Assessment? assessment = null,
        AssessmentResponse? assessmentResponse = null) => entity is null
        ? null
        : new PatientAssessmentDto
        {
            Id = entity.Id,
            AssessmentId = entity.AssessmentId,
            Assessment = (entity.Assessment ?? assessment)?.ToDto(),
            AssessmentResponseId = entity.AssessmentResponseId,
            AssessmentResponse = (entity.AssessmentResponse ?? assessmentResponse)?.ToDto(),
            Status = entity.Status,
            CompletedAt = entity.CompletedAt,
        };

    public static PatientAssessmentListItemDto? ToLightDto(this PatientAssessment? entity,
        Assessment? assessment = null) =>
        entity is null
            ? null
            : new PatientAssessmentListItemDto
            {
                Id = entity.Id,
                Name = (entity.Assessment ?? assessment)?.Name,
                Status = entity.Status,
                CompletedAt = entity.CompletedAt?.DateTime,
                CreatedAt = entity.CreatedAt.DateTime,
                Scores = entity.AssessmentResponse?.Scores.Select(s => s.ToDto()).ToList() ?? []
            };
}