using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Mappers;

public static class PatientMappers
{
    public static PatientDto? ToDto(this Patient? entity) => entity is null
        ? null
        : new PatientDto
        {
            Id = entity.Id,
            FirstName = entity.FirstName,
            MiddleName = entity.MiddleName,
            LastName = entity.LastName,
            Status = entity.Status,
            BirthDate = entity.BirthDate.ToDateTime(TimeOnly.MinValue),
            Gender = entity.Gender,
            ContactEmail = entity.ContactEmail,
            ContactPhone = entity.ContactPhone
        };
}