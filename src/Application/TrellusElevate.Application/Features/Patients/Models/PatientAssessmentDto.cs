using TrellusElevate.Application.Features.AssessmentResponses.Models;
using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Models;

public sealed class PatientAssessmentDto
{
    public Guid Id { get; set; }
    public Guid AssessmentId { get; set; }
    public AssessmentDto? Assessment { get; set; }
    public Guid? AssessmentResponseId { get; set; }
    public AssessmentResponseDto? AssessmentResponse { get; set; }
    public PatientAssessmentStatus Status { get; set; }
    public DateTimeOffset? CompletedAt { get; set; }
}