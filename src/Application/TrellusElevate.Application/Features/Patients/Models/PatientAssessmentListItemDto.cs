using TrellusElevate.Application.Features.AssessmentResponses.Models;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Models;

public sealed class PatientAssessmentListItemDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public PatientAssessmentStatus Status { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    public List<AssessmentResponseScoreDto> Scores { get; set; } = [];
}