using TrellusElevate.Core.Common.Enums;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Application.Features.Patients.Models;

public sealed class PatientListItemDto
{
    public Guid Id { get; set; }
    public string FirstName { get; set; } = null!;
    public string? MiddleName { get; set; }
    public string LastName { get; set; } = null!;
    public DateTime BirthDate { get; set; }
    public Gender Gender { get; set; }
    public PatientStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
}