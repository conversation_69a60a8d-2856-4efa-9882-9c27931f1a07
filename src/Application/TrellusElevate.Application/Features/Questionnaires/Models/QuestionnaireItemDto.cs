using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Application.Features.Questionnaires.Models;

public sealed class QuestionnaireItemDto
{
    public Guid Id { get; set; }
    public Guid? ParentQuestionnaireItemId { get; set; }
    public string LinkId { get; set; } = null!;
    public string Text { get; set; } = null!;
    public QuestionType Type { get; set; }
    public int OrderNumber { get; set; }
    public bool Required { get; set; }
    public List<EnableWhen> EnableWhen { get; set; } = null!;
    public EnableWhenBehavior? EnableBehavior { get; set; }
    public DisabledDisplay? DisabledDisplay { get; set; }
    public string? Prefix { get; set; }
    public bool Repeats { get; set; }
    public int? MaxLength { get; set; }
    public bool ReadOnly { get; set; }
    public UiElementType UiElementType { get; set; }
    public List<AnswerOption> Options { get; set; } = null!;
    public QuestionAnswer? Initial { get; set; }
    public List<QuestionnaireItemDto> Items { get; set; } = [];

    /// <summary>
    /// Any additional data (key/value)
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}