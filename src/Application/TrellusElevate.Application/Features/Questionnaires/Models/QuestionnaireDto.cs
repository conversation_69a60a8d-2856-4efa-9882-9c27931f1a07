using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Models;

public sealed class QuestionnaireDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = null!;
    public string Title { get; set; } = null!;
    public QuestionnaireStatus Status { get; set; }
    public string? Description { get; set; }
    public List<QuestionnaireItemDto> Items { get; set; } = null!;

    /// <summary>
    /// Any additional data (key/value)
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }

    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset? UpdatedAt { get; set; }
}