using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Models;

public sealed class QuestionnaireListItemDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = null!;
    public string Name { get; set; } = null!;
    public QuestionnaireStatus Status { get; set; }
    public DateTimeOffset CreatedAt { get; set; }
    public DateTimeOffset? UpdatedAt { get; set; }
}