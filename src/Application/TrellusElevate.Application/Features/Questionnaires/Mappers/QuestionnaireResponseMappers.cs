using TrellusElevate.Application.Features.QuestionnaireResponses.Models;
using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Mappers;

public static class QuestionnaireResponseMappers
{
    public static QuestionnaireResponseDto ToDto(this QuestionnaireResponse entity)
    {
        var dto = new QuestionnaireResponseDto
        {
            Id = entity.Id,
            QuestionnaireId = entity.QuestionnaireId,
            Status = entity.Status,
            CreatedAt = entity.CreatedAt,
            Items = []
        };
        
        // Only add root level items (those without a parent)
        var rootItems = entity.Items
            .Where(item => item.ParentQuestionnaireResponseItemId == null)
            .OrderBy(item => item.QuestionnaireItem?.OrderNumber);

        foreach (var rootItem in rootItems)
        {
            dto.Items.Add(rootItem.ToDto(entity.Items.ToList()));
        }

        return dto;
    }

    public static QuestionnaireResponseItemDto ToDto(this QuestionnaireResponseItem entity,
        List<QuestionnaireResponseItem>? allItems = null)
    {
        var dto = new QuestionnaireResponseItemDto
        {
            Id = entity.Id,
            QuestionnaireItemId = entity.QuestionnaireItemId,
            LinkId = entity.QuestionnaireItem?.LinkId,
            Text = entity.QuestionnaireItem?.Text,
            Answers = entity.Answers,
            OrderNumber = entity.QuestionnaireItem?.OrderNumber ?? 0,
            Items = []
        };
        
        if (allItems is null) return dto;

        // Find all child items of this item and add them recursively
        var childItems = allItems
            .Where(child => child.ParentQuestionnaireResponseItemId == dto.Id)
            .OrderBy(child => child.QuestionnaireItem?.OrderNumber);

        foreach (var childItem in childItems)
        {
            dto.Items.Add(childItem.ToDto(allItems));
        }

        return dto;
    }
}