using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Mappers;

public static class QuestionnaireMappers
{
    public static QuestionnaireDto ToDto(this Questionnaire questionnaire)
    {
        var dto = new QuestionnaireDto
        {
            Id = questionnaire.Id,
            Name = questionnaire.Name,
            Title = questionnaire.Title,
            Status = questionnaire.Status,
            Description = questionnaire.Description,
            Metadata = questionnaire.Metadata,
            CreatedAt = questionnaire.CreatedAt,
            UpdatedAt = questionnaire.UpdatedAt,
            Items = []
        };

        // Only add root level items (those without a parent)
        var rootItems = questionnaire.Items
            .Where(item => item.ParentQuestionnaireItemId == null)
            .OrderBy(item => item.OrderNumber);

        foreach (var rootItem in rootItems)
        {
            dto.Items.Add(rootItem.ToDto(questionnaire.Items.ToList()));
        }

        return dto;
    }

    public static QuestionnaireItemDto ToDto(this QuestionnaireItem item, List<QuestionnaireItem>? allItems = null)
    {
        var dto = new QuestionnaireItemDto
        {
            Id = item.Id,
            ParentQuestionnaireItemId = item.ParentQuestionnaireItemId,
            LinkId = item.LinkId,
            Text = item.Text,
            Type = item.Type,
            OrderNumber = item.OrderNumber,
            Required = item.Required,
            EnableBehavior = item.EnableBehavior,
            DisabledDisplay = item.DisabledDisplay,
            Prefix = item.Prefix,
            MaxLength = item.MaxLength,
            ReadOnly = item.ReadOnly,
            Repeats = item.Repeats,
            UiElementType = item.UiElementType,
            Initial = item.Initial,
            Metadata = item.Metadata,
            EnableWhen = item.EnableWhen.ToList(),
            Options = item.Options.ToList(),
            Items = []
        };

        if (allItems is null) return dto;

        // Find all child items of this item and add them recursively
        var childItems = allItems
            .Where(child => child.ParentQuestionnaireItemId == item.Id)
            .OrderBy(child => child.OrderNumber);

        foreach (var childItem in childItems)
        {
            dto.Items.Add(childItem.ToDto(allItems));
        }

        return dto;
    }
}