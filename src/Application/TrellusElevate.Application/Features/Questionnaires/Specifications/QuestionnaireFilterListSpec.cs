using TrellusElevate.Application.Features.Questionnaires.Enums;
using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Specifications;

public sealed class QuestionnaireFilterListSpec : Specification<Questionnaire, QuestionnaireListItemDto>
{
    public QuestionnaireFilterListSpec(string? searchTerm,
        List<QuestionnaireStatus>? status,
        QuestionnaireSort? sortBy,
        SortDirection orderDirection,
        int page,
        int pageSize)
    {
        if (!string.IsNullOrEmpty(searchTerm))
        {
            Query.Where(p =>
                p.Name.Contains(searchTerm) ||
                p.Description!.Contains(searchTerm));
        }

        if (status != null && status.Count != 0)
        {
            Query.Where(p => status.Contains(p.Status));
        }

        sortBy ??= QuestionnaireSort.CreatedAt;

        switch (sortBy)
        {
            case QuestionnaireSort.Status:
                Query.ApplyOrdering(s => s.Status, orderDirection);
                break;
            case QuestionnaireSort.Title:
                Query.ApplyOrdering(s => s.Title, orderDirection);
                break;
            case QuestionnaireSort.UpdatedAt:
                Query.ApplyOrdering(s => s.UpdatedAt, orderDirection);
                break;
            case QuestionnaireSort.CreatedAt:
                Query.ApplyOrdering(s => s.CreatedAt, orderDirection);
                break;
            default:
                Query.ApplyOrdering(s => s.Id, orderDirection);
                break;
        }

        Query.Skip((page - 1) * pageSize).Take(pageSize);

        Query.Select(s => new QuestionnaireListItemDto
        {
            Id = s.Id,
            Title = s.Title,
            Name = s.Name,
            Status = s.Status,
            CreatedAt = s.CreatedAt,
            UpdatedAt = s.UpdatedAt
        });
    }
}