using TrellusElevate.Application.Features.Questionnaires.Enums;
using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Application.Features.Questionnaires.Specifications;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Queries;

public sealed record GetQuestionnaireListQuery : PaginatedFilterQuery<QuestionnaireSort>,
    IQuery<Result<PaginatedResultDto<QuestionnaireListItemDto>>>
{
    public IEnumerable<QuestionnaireStatus>? Status { get; set; }
}

public sealed class GetQuestionnaireListQueryHandler(IRepository<Questionnaire> repository)
    : IQueryHandler<GetQuestionnaireListQuery, Result<PaginatedResultDto<QuestionnaireListItemDto>>>
{
    public async ValueTask<Result<PaginatedResultDto<QuestionnaireListItemDto>>> Handle(
        GetQuestionnaireListQuery request,
        CancellationToken cancellationToken)
    {
        var totalCount = await repository.CountAsync(new QuestionnaireFilterListSpec(
            request.SearchTerm,
            request.Status?.ToList(),
            null,
            SortDirection.Asc,
            1,
            int.MaxValue
        ), cancellationToken);

        var items = await repository.ListAsync(new QuestionnaireFilterListSpec(
            request.SearchTerm,
            request.Status?.ToList(),
            request.SortBy,
            request.SortDirection,
            request.Page,
            request.PageSize
        ), cancellationToken);

        return new PaginatedResultDto<QuestionnaireListItemDto>(request.Page, request.PageSize, totalCount, items);
    }
}