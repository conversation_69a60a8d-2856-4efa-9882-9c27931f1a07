using TrellusElevate.Application.Features.Questionnaires.Mappers;
using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Core.Domains.Questionnaires;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Queries;

public sealed record GetQuestionnaireByIdQuery(Guid Id) : IQuery<Result<QuestionnaireDto>>;

public sealed class GetQuestionnaireByIdQueryHandler(IReadRepository<Questionnaire> repository)
    : IQueryHandler<GetQuestionnaireByIdQuery, Result<QuestionnaireDto>>
{
    public async ValueTask<Result<QuestionnaireDto>> Handle(GetQuestionnaireByIdQuery query,
        CancellationToken cancellationToken)
    {
        var questionnaire = await repository.GetByIdAsync(query.Id, cancellationToken);

        if (questionnaire is null) return QuestionnaireErrors.NotFound(query.Id);

        return questionnaire.ToDto();
    }
}