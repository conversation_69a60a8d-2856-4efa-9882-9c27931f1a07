using TrellusElevate.Application.Features.Questionnaires.Mappers;
using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Application.Features.Questionnaires.Specifications;
using TrellusElevate.Core.Domains.Questionnaires;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Commands;

public sealed record CreateQuestionnaireCommand(
    string Name,
    string Title,
    string? Description,
    Dictionary<string, object>? Metadata
) : ICommand<Result<QuestionnaireDto>>;

public sealed class CreateQuestionnaireCommandHandler(IRepository<Questionnaire> repository)
    : ICommandHandler<CreateQuestionnaireCommand, Result<QuestionnaireDto>>
{
    public async ValueTask<Result<QuestionnaireDto>> Handle(CreateQuestionnaireCommand command,
        CancellationToken cancellationToken)
    {
        if (await repository.AnyAsync(new QuestionnaireByNameSpec(command.Name), cancellationToken))
        {
            return QuestionnaireErrors.Exists(command.Name);
        }
        
        var entityResult = Questionnaire.Create(command.Name, command.Title, command.Description, 
            QuestionnaireStatus.Draft,
            command.Metadata);

        if (entityResult.IsError) return entityResult.Error;

        var result = await repository.AddAsync(entityResult.Value, cancellationToken);
  
        return result.ToDto();
    }
}