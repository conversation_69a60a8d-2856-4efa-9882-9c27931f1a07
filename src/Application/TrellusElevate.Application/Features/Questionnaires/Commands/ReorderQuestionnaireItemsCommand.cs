using TrellusElevate.Core.Domains.Questionnaires;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Commands;

public sealed record ReorderQuestionnaireItemsCommand(
    Guid QuestionnaireId,
    Dictionary<Guid, int> OrderMap
) : ICommand<Result>;

public sealed class ReorderQuestionnaireItemsCommandHandler(IRepository<Questionnaire> repository)
    : ICommandHandler<ReorderQuestionnaireItemsCommand, Result>
{
    public async ValueTask<Result> Handle(ReorderQuestionnaireItemsCommand command,
        CancellationToken cancellationToken)
    {
        var questionnaire = await repository.GetByIdAsync(command.QuestionnaireId, cancellationToken);

        if (questionnaire is null) return QuestionnaireErrors.NotFound(command.QuestionnaireId);

        var result = questionnaire.ReorderQuestionnaireItems(command.OrderMap);
        if (result.IsError) return result.Error;
        
        await repository.UpdateAsync(questionnaire, cancellationToken);

        return Result.Ok();
    }
}