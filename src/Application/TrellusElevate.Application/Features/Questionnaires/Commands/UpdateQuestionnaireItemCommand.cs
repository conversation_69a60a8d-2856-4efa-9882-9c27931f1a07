using TrellusElevate.Application.Features.Questionnaires.Mappers;
using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Core.Domains.Questionnaires;
using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Application.Features.Questionnaires.Commands;

public sealed record UpdateQuestionnaireItemCommand(
    Guid QuestionnaireId,
    Guid QuestionnaireItemId,
    string LinkId,
    string Text,
    QuestionType Type,
    int OrderNumber,
    bool Required,
    List<EnableWhen> EnableWhen,
    EnableWhenBehavior? EnableBehavior,
    DisabledDisplay? DisabledDisplay,
    string? Prefix,
    int? Max<PERSON>ength,
    bool ReadOnly,
    bool Repeats,
    UiElementType UiElementType,
    List<AnswerOption>? Options,
    QuestionAnswer? Initial,
    Dictionary<string, object>? Metadata
) : ICommand<Result<QuestionnaireItemDto>>;

public sealed class UpdateQuestionnaireItemCommandHandler(IRepository<Questionnaire> repository)
    : ICommandHandler<UpdateQuestionnaireItemCommand, Result<QuestionnaireItemDto>>
{
    public async ValueTask<Result<QuestionnaireItemDto>> Handle(UpdateQuestionnaireItemCommand command,
        CancellationToken cancellationToken)
    {
        var questionnaire = await repository.GetByIdAsync(command.QuestionnaireId, cancellationToken);

        if (questionnaire is null) return QuestionnaireErrors.NotFound(command.QuestionnaireId);

        var item = questionnaire.Items.FirstOrDefault(i => i.Id == command.QuestionnaireItemId);
        if (item is null)
            return QuestionnaireErrors.QuestionnaireItemNotFound(command.QuestionnaireItemId);

        var result = questionnaire.UpdateQuestionnaireItem(
            command.QuestionnaireItemId,
            command.LinkId,
            command.Text,
            command.Type,
            command.OrderNumber,
            command.Required,
            command.EnableWhen,
            command.EnableBehavior,
            command.DisabledDisplay,
            command.Prefix,
            command.MaxLength,
            command.ReadOnly,
            command.Repeats,
            command.UiElementType,
            command.Options ?? [],
            command.Initial,
            command.Metadata
        );

        if (result.IsError) return result.Error;

        await repository.UpdateAsync(questionnaire, cancellationToken);

        return item.ToDto();
    }
}