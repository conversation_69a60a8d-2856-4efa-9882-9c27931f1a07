using TrellusElevate.Core.Domains.Questionnaires;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Commands;

public sealed record RemoveQuestionnaireItemCommand(
    Guid QuestionnaireId,
    Guid QuestionnaireItemId
) : ICommand<Result>;

public sealed class RemoveQuestionnaireItemCommandHandler(IRepository<Questionnaire> repository)
    : ICommandHandler<RemoveQuestionnaireItemCommand, Result>
{
    public async ValueTask<Result> Handle(RemoveQuestionnaireItemCommand command,
        CancellationToken cancellationToken)
    {
        var questionnaire = await repository.GetByIdAsync(command.QuestionnaireId, cancellationToken);

        if (questionnaire is null) return QuestionnaireErrors.NotFound(command.QuestionnaireId);

        var result = questionnaire.RemoveQuestionnaireItem(command.QuestionnaireItemId);
        if (result.IsError) return result.Error;

        await repository.UpdateAsync(questionnaire, cancellationToken: cancellationToken);

        return Result.Ok();
    }
}