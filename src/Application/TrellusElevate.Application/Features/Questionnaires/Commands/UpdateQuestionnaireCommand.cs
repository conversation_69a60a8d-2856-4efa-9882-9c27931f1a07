using TrellusElevate.Application.Features.Questionnaires.Mappers;
using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Application.Features.Questionnaires.Specifications;
using TrellusElevate.Core.Domains.Questionnaires;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Application.Features.Questionnaires.Commands;

public sealed record UpdateQuestionnaireCommand(
    Guid Id,
    string Name,
    string Title,
    string? Description,
    QuestionnaireStatus Status,
    Dictionary<string, object>? Metadata
) : ICommand<Result<QuestionnaireDto>>;

public sealed class UpdateQuestionnaireCommandHandler(IRepository<Questionnaire> repository)
    : ICommandHandler<UpdateQuestionnaireCommand, Result<QuestionnaireDto>>
{
    public async ValueTask<Result<QuestionnaireDto>> Handle(UpdateQuestionnaireCommand command,
        CancellationToken cancellationToken)
    {
        var entity = await repository.GetByIdAsync(command.Id, cancellationToken);

        if (entity is null) return QuestionnaireErrors.NotFound(command.Id);

        if (command.Name != entity.Name &&
            await repository.AnyAsync(new QuestionnaireByNameSpec(command.Name), cancellationToken))
            return QuestionnaireErrors.Exists(command.Name);

        var result = entity.Update(command.Name, command.Title, command.Description, command.Status, command.Metadata);

        if (result.IsError) return result.Error;

        await repository.UpdateAsync(entity, cancellationToken);

        return entity.ToDto();
    }
}