using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using TrellusElevate.Application.Common.Mediator;

namespace TrellusElevate.Application;

public static class Configuration
{
    public static IServiceCollection AddApplication(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton(TimeProvider.System);
        
        return services
            .AddCache()
            .AddClients()
            .AddMediator();
    }

    private static IServiceCollection AddClients(this IServiceCollection services)
    {
        return services;
    }

    private static IServiceCollection AddCache(this IServiceCollection services)
    {
        return services.AddMemoryCache();
    }

    private static IServiceCollection AddMediator(this IServiceCollection services)
    {
        services.AddMediator(options =>
        {
            options.PipelineBehaviors = [typeof(LoggingBehavior<,>)];
            options.ServiceLifetime = ServiceLifetime.Transient;
        });

        return services;
    }
}