<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="Required" xml:space="preserve">
        <value>{PropertyName} is required</value>
    </data>
    <data name="MaxLength" xml:space="preserve">
        <value>Maximum length of {PropertyName} is {MaxLength}</value>
    </data>
    <data name="MinLength" xml:space="preserve">
        <value>Minimum length of {PropertyName} is {MinLength}</value>
    </data>
    <data name="InvalidValue" xml:space="preserve">
        <value>{PropertyName} has invalid value {PropertyValue}</value>
    </data>
    <data name="InvalidLength" xml:space="preserve">
        <value>Length of {PropertyName} must be {ComparisonValue}</value>
    </data>
    <data name="LessThanOrEqualTo" xml:space="preserve">
        <value>{PropertyName} must be less or equal to {ComparisonValue}</value>
    </data>
    <data name="GreaterThan" xml:space="preserve">
        <value>{PropertyName} must be greater than {ComparisonValue}</value>
    </data>
    <data name="GreaterThanOrEqualTo" xml:space="preserve">
        <value>{PropertyName} must be greater or equal to {ComparisonValue}</value>
    </data>
    <data name="StartDateMustBeBeforeOrEqualToEndDate" xml:space="preserve">
        <value>Start date must be before or equal to the end date</value>
    </data>
</root>