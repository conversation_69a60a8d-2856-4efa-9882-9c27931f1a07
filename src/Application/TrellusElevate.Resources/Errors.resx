<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">
            
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="QuestionnaireExist" xml:space="preserve">
        <value>Questionnaire {0} exist</value>
    </data>
    <data name="QuestionnaireNotFound" xml:space="preserve">
        <value>Questionnaire {0} not found</value>
    </data>
    <data name="QuestionnaireItemNotFound" xml:space="preserve">
        <value>Questionnaire item {0} not found</value>
    </data>
    <data name="QuestionnaireResponseNotFound" xml:space="preserve">
        <value>Questionnaire response {0} not found</value>
    </data>
    <data name="AssessmentNotFound" xml:space="preserve">
        <value>Assessment {0} not found</value>
    </data>
    <data name="AssessmentScoringProfileNotFound" xml:space="preserve">
        <value>Assessment Scoring Profile {0} not found</value>
    </data>
</root>