//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TrellusElevate.Resources {
    using System;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Errors {
        
        private static System.Resources.ResourceManager resourceMan;
        
        private static System.Globalization.CultureInfo resourceCulture;
        
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Errors() {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Resources.ResourceManager ResourceManager {
            get {
                if (object.Equals(null, resourceMan)) {
                    System.Resources.ResourceManager temp = new System.Resources.ResourceManager("TrellusElevate.Resources.Errors", typeof(Errors).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        public static string QuestionnaireExist {
            get {
                return ResourceManager.GetString("QuestionnaireExist", resourceCulture);
            }
        }
        
        public static string QuestionnaireNotFound {
            get {
                return ResourceManager.GetString("QuestionnaireNotFound", resourceCulture);
            }
        }
        
        public static string QuestionnaireItemNotFound {
            get {
                return ResourceManager.GetString("QuestionnaireItemNotFound", resourceCulture);
            }
        }
        
        public static string QuestionnaireResponseNotFound {
            get {
                return ResourceManager.GetString("QuestionnaireResponseNotFound", resourceCulture);
            }
        }
        
        public static string AssessmentNotFound {
            get {
                return ResourceManager.GetString("AssessmentNotFound", resourceCulture);
            }
        }
        
        public static string AssessmentScoringProfileNotFound {
            get {
                return ResourceManager.GetString("AssessmentScoringProfileNotFound", resourceCulture);
            }
        }
    }
}
