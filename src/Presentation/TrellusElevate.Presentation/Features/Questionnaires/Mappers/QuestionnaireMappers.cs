using TrellusElevate.Application.Features.Questionnaires.Commands;
using TrellusElevate.Presentation.Features.Questionnaires.Models;

namespace TrellusElevate.Presentation.Features.Questionnaires.Mappers;

public static class QuestionnaireMappers
{
    public static CreateQuestionnaireCommand ToCommand(this SaveQuestionnaireRequest request) =>
        new(request.Name,
            request.Title,
            request.Description,
            request.Metadata);

    public static UpdateQuestionnaireCommand ToCommand(this SaveQuestionnaireRequest request, Guid id) =>
        new(id,
            request.Name,
            request.Title,
            request.Description,
            request.Status,
            request.Metadata);

    public static CreateQuestionnaireItemCommand ToCommand(this SaveQuestionnaireItemRequest request,
        Guid questionnaireId) =>
        new(questionnaireId,
            request.ParentQuestionnaireItemId,
            request.LinkId,
            request.Text,
            request.Type,
            request.OrderNumber,
            request.Required,
            request.EnableWhen,
            request.EnableBehavior,
            request.DisabledDisplay,
            request.Prefix,
            request.MaxLength,
            request.ReadOnly,
            request.Repeats,
            request.UiElementType,
            request.Options,
            request.Initial,
            request.Metadata
        );

    public static UpdateQuestionnaireItemCommand ToCommand(this SaveQuestionnaireItemRequest request,
        Guid questionnaireId, Guid id) =>
        new(questionnaireId,
            id,
            request.LinkId,
            request.Text,
            request.Type,
            request.OrderNumber,
            request.Required,
            request.EnableWhen,
            request.EnableBehavior,
            request.DisabledDisplay,
            request.Prefix,
            request.MaxLength,
            request.ReadOnly,
            request.Repeats,
            request.UiElementType,
            request.Options,
            request.Initial,
            request.Metadata
        );
}