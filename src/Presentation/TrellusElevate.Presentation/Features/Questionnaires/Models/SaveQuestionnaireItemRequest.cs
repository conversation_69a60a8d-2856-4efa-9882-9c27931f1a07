using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.Questionnaires.ValueObjects;

namespace TrellusElevate.Presentation.Features.Questionnaires.Models;

public sealed class SaveQuestionnaireItemRequest
{
    public Guid? ParentQuestionnaireItemId { get; set; }
    public string LinkId { get; set; } = null!;
    public string Text { get; set; } = null!;
    public QuestionType Type { get; set; }
    public int OrderNumber { get; set; }
    public bool Required { get; set; }
    public List<EnableWhen> EnableWhen { get; set; } = [];
    public EnableWhenBehavior? EnableBehavior { get; set; }
    public DisabledDisplay? DisabledDisplay { get; set; }
    public string? Prefix { get; set; }
    public bool Repeats { get; set; }
    public int? MaxLength { get; set; }
    public bool ReadOnly { get; set; }
    public UiElementType UiElementType { get; set; }
    public List<AnswerOption> Options { get; set; } = [];
    public QuestionAnswer? Initial { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public sealed class SaveQuestionnaireItemRequestValidator : AbstractValidator<SaveQuestionnaireItemRequest>
{
    public SaveQuestionnaireItemRequestValidator()
    {
        RuleFor(s => s.LinkId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.Text)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.Type)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
    }
}