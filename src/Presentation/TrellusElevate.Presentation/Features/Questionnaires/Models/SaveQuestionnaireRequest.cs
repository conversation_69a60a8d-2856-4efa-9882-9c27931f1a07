using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Core.Domains.Questionnaires.Entities;

namespace TrellusElevate.Presentation.Features.Questionnaires.Models;

public sealed class SaveQuestionnaireRequest
{
    public string Name { get; set; } = null!;
    public string Title { get; set; } = null!;
    public QuestionnaireStatus Status { get; set; }
    public string? Description { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class SaveQuestionnaireRequestValidator : AbstractValidator<SaveQuestionnaireRequest>
{
    public SaveQuestionnaireRequestValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Title)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Status)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}

public sealed class QuestionnaireItemValidator : AbstractValidator<QuestionnaireItemDto>
{
    public QuestionnaireItemValidator()
    {
        RuleFor(s => s.LinkId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.Text)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.Type)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
    }
}