using TrellusElevate.Application.Features.Assessments.Models;

namespace TrellusElevate.Presentation.Features.Assessments.Models;

public sealed class SaveAssessmentScoreMappingRequest
{
    public List<AssessmentScoreMappingDto> Mappings { get; set; } = null!;
}

public sealed class SaveAssessmentScoreMappingRequestValidator : AbstractValidator<SaveAssessmentScoreMappingRequest>
{
    public SaveAssessmentScoreMappingRequestValidator()
    {
        RuleFor(s => s.Mappings)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleForEach(x => x.Mappings)
            .SetValidator(new AssessmentScoreMappingValidator());
    }
}

public sealed class AssessmentScoreMappingValidator : AbstractValidator<AssessmentScoreMappingDto>
{
    public AssessmentScoreMappingValidator()
    {
        RuleFor(x => x.QuestionnaireItemId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Answers)
            .NotNull()
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Operator)
            .NotNull()
            .WithMessage(Resources.Validations.Required);

        RuleFor(x => x.Weight)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
    }
}