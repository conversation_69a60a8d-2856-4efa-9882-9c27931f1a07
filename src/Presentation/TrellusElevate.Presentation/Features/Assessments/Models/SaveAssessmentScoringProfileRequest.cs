using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Presentation.Features.Assessments.Models;

public sealed class SaveAssessmentScoringProfileRequest
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public string? CalculationFormula { get; set; }
    public AssessmentScoringProfileStatus Status { get; set; }
}

public sealed class SaveAssessmentScoringProfileRequestValidator 
    : AbstractValidator<SaveAssessmentScoringProfileRequest>
{
    public SaveAssessmentScoringProfileRequestValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);

        When(x => !string.IsNullOrEmpty(x.CalculationFormula), () =>
        {
            RuleFor(x => x.CalculationFormula)
                .Must(x => !new NCalc.Expression(x).HasErrors())
                .WithMessage("Invalid calculation formula.");
        });

    }
}