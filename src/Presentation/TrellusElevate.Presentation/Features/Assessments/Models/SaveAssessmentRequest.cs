using TrellusElevate.Core.Domains.Assessments.Entities;

namespace TrellusElevate.Presentation.Features.Assessments.Models;

public sealed class SaveAssessmentRequest
{
    public string Name { get; set; } = null!;
    public string? Description { get; set; }
    public Guid QuestionnaireId { get; set; }
    public AssessmentStatus Status { get; set; }
}

public sealed class SaveAssessmentRequestValidator : AbstractValidator<SaveAssessmentRequest>
{
    public SaveAssessmentRequestValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        
        RuleFor(x => x.QuestionnaireId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}