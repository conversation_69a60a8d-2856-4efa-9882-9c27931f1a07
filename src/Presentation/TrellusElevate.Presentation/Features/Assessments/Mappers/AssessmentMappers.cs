using TrellusElevate.Application.Features.Assessments.Commands;
using TrellusElevate.Presentation.Features.Assessments.Models;

namespace TrellusElevate.Presentation.Features.Assessments.Mappers;

public static class AssessmentMappers
{
    public static CreateAssessmentCommand ToCommand(this SaveAssessmentRequest request) =>
        new(
            request.Name,
            request.Description,
            request.QuestionnaireId
        );

    public static UpdateAssessmentCommand ToCommand(this SaveAssessmentRequest request, Guid id) =>
        new(
            id,
            request.Name,
            request.Description,
            request.QuestionnaireId,
            request.Status
        );

    public static CreateAssessmentScoringProfileCommand ToCommand(this SaveAssessmentScoringProfileRequest request,
        Guid assessmentId) =>
        new(
            assessmentId,
            request.Name,
            request.Description
        );

    public static UpdateAssessmentScoringProfileCommand ToCommand(this SaveAssessmentScoringProfileRequest request,
        Guid assessmentId, Guid scoringProfileId) =>
        new(
            assessmentId,
            scoringProfileId,
            request.Name,
            request.Description,
            request.CalculationFormula,
            request.Status
        );
}