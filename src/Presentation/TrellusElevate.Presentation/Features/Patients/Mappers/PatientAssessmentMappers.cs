using TrellusElevate.Application.Features.Patients.Commands;
using TrellusElevate.Presentation.Features.Patients.Models;

namespace TrellusElevate.Presentation.Features.Patients.Mappers;

public static class PatientAssessmentMappers
{
    public static CreatePatientAssessmentCommand ToCommand(this CreatePatientAssessmentRequest request, Guid patientId) 
        => new(patientId, request.AssessmentId);
}