using TrellusElevate.Application.Common.Extensions;
using TrellusElevate.Application.Features.Patients.Commands;
using TrellusElevate.Presentation.Features.Patients.Models;

namespace TrellusElevate.Presentation.Features.Patients.Mappers;

public static class PatientMappers
{
    public static CreatePatientCommand ToCommand(this CreatePatientRequest request)
    {
        return new CreatePatientCommand(
            request.FirstName,
            request.MiddleName,
            request.LastName,
            request.BirthDate!.Value.ToDateOnly(),
            request.Gender!.Value,
            request.Status,
            request.ContactEmail,
            request.ContactPhone);
    }

    public static UpdatePatientCommand ToCommand(this UpdatePatientRequest request, Guid id)
    {
        return new UpdatePatientCommand(
            id,
            request.FirstName,
            request.MiddleName,
            request.LastName,
            request.BirthDate!.Value.ToDateOnly(),
            request.Gender!.Value,
            request.ContactEmail,
            request.ContactPhone);
    }
}