using TrellusElevate.Core.Common.Enums;

namespace TrellusElevate.Presentation.Features.Patients.Models;

public sealed class UpdatePatientRequest
{
    public string FirstName { get; set; } = null!;
    public string? MiddleName { get; set; }
    public string LastName { get; set; } = null!;
    public DateTime? BirthDate { get; set; }
    public Gender? Gender { get; set; }
    public string ContactEmail { get; set; } = null!;
    public string ContactPhone { get; set; } = null!;
}

public sealed class UpdatePatientRequestValidator : AbstractValidator<UpdatePatientRequest>
{
    public UpdatePatientRequestValidator()
    {
        RuleFor(s => s.FirstName)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.LastName)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.BirthDate)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.Gender)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.ContactEmail)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.ContactPhone)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}