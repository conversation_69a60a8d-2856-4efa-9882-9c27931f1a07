namespace TrellusElevate.Presentation.Features.Patients.Models;

public sealed class CreatePatientAssessmentRequest
{
    public Guid AssessmentId { get; set; }
}

public sealed class CreatePatientAssessmentRequestValidator : AbstractValidator<CreatePatientAssessmentRequest>
{
    public CreatePatientAssessmentRequestValidator()
    {
        RuleFor(s => s.AssessmentId)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}