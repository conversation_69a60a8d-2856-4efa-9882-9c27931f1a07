using TrellusElevate.Core.Common.Enums;
using TrellusElevate.Core.Domains.Patients.Entities;

namespace TrellusElevate.Presentation.Features.Patients.Models;

public sealed class CreatePatientRequest
{
    public string FirstName { get; set; } = null!;
    public string? MiddleName { get; set; }
    public string LastName { get; set; } = null!;
    public DateTime? BirthDate { get; set; }
    public Gender? Gender { get; set; }
    public PatientStatus Status { get; set; }
    public string ContactEmail { get; set; } = null!;
    public string ContactPhone { get; set; } = null!;
}

public sealed class CreatePatientRequestValidator : AbstractValidator<CreatePatientRequest>
{
    public CreatePatientRequestValidator()
    {
        RuleFor(s => s.FirstName)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.LastName)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.BirthDate)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.Gender)
            .NotNull()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.ContactEmail)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
        RuleFor(s => s.ContactPhone)
            .NotEmpty()
            .WithMessage(Resources.Validations.Required);
    }
}