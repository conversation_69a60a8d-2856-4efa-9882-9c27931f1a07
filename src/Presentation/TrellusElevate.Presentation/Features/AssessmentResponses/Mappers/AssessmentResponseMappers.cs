using TrellusElevate.Application.Features.AssessmentResponses.Commands;
using TrellusElevate.Presentation.Features.AssessmentResponses.Models;

namespace TrellusElevate.Presentation.Features.AssessmentResponses.Mappers;

public static class AssessmentResponseMappers
{
    public static CreateAssessmentResponseCommand ToCommand(this CreateAssessmentResponseRequest request) =>
        new(request.AssessmentId, request.QuestionnaireResponse);
}