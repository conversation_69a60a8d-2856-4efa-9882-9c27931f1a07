using System.Reflection;
using Destructurama;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using OpenTelemetry;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Serilog;
using SharpGrip.FluentValidation.AutoValidation.Mvc.Extensions;

namespace TrellusElevate.Presentation;

public static class Configuration
{
    public static WebApplicationBuilder AddPresentation(this WebApplicationBuilder builder)
    {
        builder.ConfigureOpenTelemetry();

        builder.Services.AddValidation();

        return builder;
    }

    private static IServiceCollection AddValidation(this IServiceCollection services)
    {
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly())
            .AddFluentValidationAutoValidation();

        return services;
    }

    private static WebApplicationBuilder ConfigureOpenTelemetry(this WebApplicationBuilder builder,
        string? serviceName = null)
    {
        serviceName ??= $"{builder.Environment.ApplicationName}.{builder.Environment.EnvironmentName}";
        builder.Host.UseSerilog(
            (_, loggerConfiguration) =>
            {
                loggerConfiguration.ReadFrom.Configuration(builder.Configuration);
                loggerConfiguration.Destructure.UsingAttributes();
                loggerConfiguration.WriteTo.OpenTelemetry(options =>
                    options.ResourceAttributes.Add("service.name", serviceName));
            });

        var otelBuilder = builder.Services.AddOpenTelemetry();

        otelBuilder.UseOtlpExporter();

        otelBuilder.ConfigureResource(resource => resource.AddService(serviceName))
            .WithMetrics(metrics =>
            {
                metrics.AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddRuntimeInstrumentation();
            })
            .WithTracing(tracing =>
            {
                tracing.AddAspNetCoreInstrumentation()
                    .AddHttpClientInstrumentation()
                    .AddEntityFrameworkCoreInstrumentation();
            });

        return builder;
    }
}