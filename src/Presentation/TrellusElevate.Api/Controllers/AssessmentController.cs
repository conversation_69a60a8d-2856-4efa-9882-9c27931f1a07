using TrellusElevate.Application.Features.Assessments.Models;
using TrellusElevate.Application.Features.Assessments.Queries;

namespace TrellusElevate.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
public sealed class AssessmentController(IMediator mediator) : ControllerBase
{
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(AssessmentDto), StatusCodes.Status200OK)]
    public async Task<IResult> GetById(Guid id, CancellationToken cancellationToken)
    {
        return (await mediator.Send(new GetAssessmentByIdQuery(id), cancellationToken)).ToResult();
    }
}