using TrellusElevate.Application.Features.AssessmentResponses.Models;
using TrellusElevate.Presentation.Features.AssessmentResponses.Mappers;
using TrellusElevate.Presentation.Features.AssessmentResponses.Models;

namespace TrellusElevate.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
public sealed class AssessmentResponseController(IMediator mediator) : ControllerBase
{
    [HttpPost]
    [ProducesResponseType(typeof(AssessmentResponseDto), StatusCodes.Status200OK)]
    public async Task<IResult> Create(CreateAssessmentResponseRequest request, CancellationToken cancellationToken)
    {
        return (await mediator.Send(request.ToCommand(), cancellationToken)).ToResult();
    }
}