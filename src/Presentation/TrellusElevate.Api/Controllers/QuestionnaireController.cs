using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Application.Features.Questionnaires.Queries;

namespace TrellusElevate.Api.Controllers;

[Route("api/[controller]")]
[ApiController]
public sealed class QuestionnaireController(IMediator mediator) : ControllerBase
{
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(QuestionnaireDto), StatusCodes.Status200OK)]
    public async Task<IResult> GetById(Guid id, CancellationToken cancellationToken)
    {
        return (await mediator.Send(new GetQuestionnaireByIdQuery(id), cancellationToken)).ToResult();
    }
    
}