using TrellusElevate.Api.Common.ErrorHandling;
using TrellusElevate.Api.Common.OpenApi;

namespace TrellusElevate.Api;

public static class Configuration
{
    public static IServiceCollection AddApi(this IServiceCollection services)
    {
        services.AddErrorHandling();
        services.AddOpenApi(opt =>
        {
            opt.AddDocumentTransformer<BearerSecuritySchemeTransformer>();
        });
        
        services.AddHttpContextAccessor();

        return services;
    }
    
}