using Microsoft.AspNetCore.Diagnostics;

namespace TrellusElevate.Api.Common.ErrorHandling;

public class ApiExceptionHandler(IWebHostEnvironment environment, IProblemDetailsService problemDetailsService)
    : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception,
        CancellationToken cancellationToken)
    {
        int statusCode = StatusCodes.Status500InternalServerError; // Default

        // Change status in case of specific exception
        // if (exception is SecurityTokenException)
        // {
        //     statusCode = StatusCodes.Status401Unauthorized;
        // }
        
        var problemDetails = new ProblemDetails
        {
            Status = statusCode, 
            Detail = exception.Message,
            Instance = $"{httpContext.Request.Method} {httpContext.Request.Path}"
        };

        if (!environment.IsProduction())
        {
            problemDetails.Extensions = new Dictionary<string, object?> { ["stackTrace"] = exception.StackTrace };
        }
        
        httpContext.Response.StatusCode = statusCode;

        return await problemDetailsService.TryWriteAsync(new ProblemDetailsContext
        {
            HttpContext = httpContext,
            ProblemDetails = problemDetails,
            Exception = exception,
        });
    }
}