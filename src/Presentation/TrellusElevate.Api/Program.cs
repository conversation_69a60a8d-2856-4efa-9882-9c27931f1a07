using System.Text.Json.Serialization;
using Scalar.AspNetCore;
using Serilog;
using TrellusElevate.Api;
using TrellusElevate.Application;
using TrellusElevate.Infrastructure;
using TrellusElevate.Presentation;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers()
    .AddJsonOptions(options => { options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter()); });

builder.AddPresentation();
builder.Services.AddApi();
builder.Services.AddInfrastructure(builder.Configuration);
builder.Services.AddApplication(builder.Configuration);

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference(o =>
    {
        o.WithTheme(ScalarTheme.Saturn);
    });
}

app.UseHttpsRedirection();

app.UseSerilogRequestLogging();

app.UseAuthentication();
app.UseAuthorization();


app.UseExceptionHandler();

app.MapControllers();

app.Run();
