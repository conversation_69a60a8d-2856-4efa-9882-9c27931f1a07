using MudBlazor.Services;
using TrellusElevate.Application;
using TrellusElevate.Infrastructure;
using TrellusElevate.Presentation;
using TrellusElevate.Web;
using TrellusElevate.Web.Components;
using MudExtensions.Services;

var builder = WebApplication.CreateBuilder(args);

// Add MudBlazor services
builder.Services.AddMudServices();
builder.Services.AddMudExtensions();

builder.AddPresentation();
builder.Services.AddInfrastructureWeb(builder.Configuration);
builder.Services.AddApplication(builder.Configuration);
builder.Services.AddWeb();

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseStatusCodePagesWithRedirects("/404");

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();