<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <InvariantGlobalization>true</InvariantGlobalization>
    </PropertyGroup>
    
    <ItemGroup>
        <PackageReference Include="CodeBeam.MudBlazor.Extensions" Version="8.2.4" />
        <PackageReference Include="MudBlazor" Version="8.10.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\TrellusElevate.Presentation\TrellusElevate.Presentation.csproj"/>
    </ItemGroup>

    <ItemGroup>
      <_ContentIncludedByDefault Remove="Components\Pages\Patients\Patient\Questionnaire\Create.razor" />
      <_ContentIncludedByDefault Remove="Components\Pages\Patients\Patient\Questionnaire\QuestionnaireFormBuilder.razor" />
    </ItemGroup>
</Project>
