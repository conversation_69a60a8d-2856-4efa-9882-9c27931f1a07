@page "/patients"
@using TrellusElevate.Application.Common.Extensions
@using TrellusElevate.Application.Common.Helpers
@using TrellusElevate.Application.Features.Patients.Models
@using TrellusElevate.Application.Features.Patients.Queries
@using TrellusElevate.Core.Domains.Patients.Entities
@using TrellusElevate.Web.Common.Extensions
@using SortDirection = TrellusElevate.Application.Common.Enums.SortDirection
@inject IMediator Mediator
@inject ISnackbar Snackbar
@inject NavigationManager NavigationManager

<PageTitle>Patients</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Patients", href: "/patients", icon: Icons.Material.Filled.People),
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add" Href="/patients/create" Variant="Variant.Filled"
               Color="Color.Primary">
        New
    </MudButton>
</MudToolBar>

<MudDataGrid @ref="_dataGrid" T="PatientListItemDto" ServerData="ServerReload" Filterable="false" Striped="true"
             Hover="true"
             SortMode="SortMode.Single" RowClick="@(row => NavigationManager.NavigateTo($"patients/{row.Item.Id}"))">
    <ToolBarContent>
        <MudStack Spacing="3" Row="true" Wrap="Wrap.Wrap">
            <MudTextField Class="pt-1"  
                          @bind-Value="_query.SearchTerm" T="string"
                          @bind-Value:after="Filter" Placeholder="Search"
                          Clearable="true"
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"/>

            <MudSelect   Dense="true" T="PatientStatus"
                       MultiSelectionTextFunc="@(val => string.Join(", ", val!.Select(s => Enum.Parse<PatientStatus>(s!).GetDescription())))"
                       Label="Status" MultiSelection="true" Clearable="true"
                       @bind-SelectedValues="_query.Status" @bind-SelectedValues:after="Filter">
                @foreach (var item in Enum.GetValues<PatientStatus>())
                {
                    <MudSelectItem T="PatientStatus" Value="item">@item.GetDescription()</MudSelectItem>
                }
            </MudSelect>
        </MudStack>
    </ToolBarContent>
    <Columns>
        <PropertyColumn Property="x => x.FirstName" Title="First Name"/>
        <PropertyColumn Property="x => x.LastName" Title="Last Name"/>
        <PropertyColumn Property="x => x.Status" Title="Status">
            <CellTemplate>
                <MudChip T="string" Color="@context.Item.Status.GetColor()">
                    @context.Item.Status.GetDescription()
                </MudChip>
            </CellTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.BirthDate" Title="Age">
            <CellTemplate> @DateHelpers.GetYearsFromDate(context.Item.BirthDate)</CellTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.Gender" Title="Sex">
            <CellTemplate>@context.Item.Gender.ToString()</CellTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.CreatedAt" Title="Start Date" Format="d"/>
    </Columns>
    <PagerContent>
        <MudDataGridPager T="PatientListItemDto"/>
    </PagerContent>
</MudDataGrid>

@code {
    private MudDataGrid<PatientListItemDto> _dataGrid = null!;
    private readonly GetPatientListQuery _query = new();

    private async Task<GridData<PatientListItemDto>> ServerReload(GridState<PatientListItemDto> state)
    {
        _query.Page = state.Page + 1;
        _query.PageSize = state.PageSize;

        var sortDefinition = state.SortDefinitions.FirstOrDefault();
        if (sortDefinition != null)
        {
            _query.SortBy = Enum.Parse<PatientSort>(sortDefinition.SortBy);
            _query.SortDirection = sortDefinition.Descending ? SortDirection.Desc : SortDirection.Asc;
        }

        var result = await Mediator.Send(_query);

        return result.Match(
            value => new GridData<PatientListItemDto>
            {
                TotalItems = value.TotalCount,
                Items = value.Items
            },
            error =>
            {
                Snackbar.Add($"Error loading patients: {error.Code}: {error.Description}", Severity.Error);
                return new GridData<PatientListItemDto>();
            });
    }

    private Task Filter()
    {
        return _dataGrid.ReloadServerData();
    }

}