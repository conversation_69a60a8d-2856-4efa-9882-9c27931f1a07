using TrellusElevate.Application.Features.Patients.Models;
using TrellusElevate.Application.Features.Patients.Queries;
using TrellusElevate.Web.Common.State;

namespace TrellusElevate.Web.Components.Pages.Patients.Patient;

public sealed class PatientState(IMediator mediator) : BaseState
{
    public Guid? PatientId { get; private set; }
    public PatientDto? Patient { get; private set; }

    public async Task<PatientDto> LoadPatientAsync(Guid patientId)
    {
        if (PatientId == patientId && Patient != null) return Patient;
        PatientId = patientId;
        var result = await mediator.Send(new GetPatientByIdQuery(patientId));

        result.Switch(
            value => Patient = value,
            SetError
        );

        NotifyStateChanged();
        return Patient!;
    }
    
    public void SetPatient(PatientDto patient)
    {
        if (PatientId != patient.Id) return;
        Patient = patient;
        NotifyStateChanged();
    }
}