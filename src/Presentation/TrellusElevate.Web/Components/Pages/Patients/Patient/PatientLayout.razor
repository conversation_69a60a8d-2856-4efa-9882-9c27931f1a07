@using TrellusElevate.Application.Common.Extensions
@using TrellusElevate.Application.Common.Helpers
@using TrellusElevate.Core.Domains.Patients.Entities
@using TrellusElevate.Web.Common.Extensions
@inherits LayoutComponentBase
@implements IDisposable
@layout Layout.MainLayout
@inject PatientState PatientState

@* @if (PatientState.Patient is null) return; *@

@if (PatientState.Error != null)
{
    <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
              CloseIconClicked="PatientState.ClearError">
        @PatientState.Error?.Code: @PatientState.Error?.Description
    </MudAlert>
}

@Body

<MudDrawer Style="top: 0" Class="mt-10 pt-4" @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2"
           Variant="@DrawerVariant.Mini">
    <MudNavMenu>
        <MudNavLink Href="@($"patients/{PatientState.PatientId}")" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.Home">Home
        </MudNavLink>

        <MudNavLink Href="@($"patients/{PatientState.PatientId}/edit")" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.Edit">Edit
        </MudNavLink>
        
        <MudNavLink Href="@($"patients/{PatientState.PatientId}/assessments")" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.Assessment">Assessments
        </MudNavLink>
    </MudNavMenu>
    <MudSpacer/>
    <MudStack Row>
        <MudSpacer/>
        <MudToggleIconButton
            ToggledChanged="@(_ => _drawerOpen = !_drawerOpen)"
            Toggled="_drawerOpen"
            Size="Size.Small"
            Icon="@Icons.Material.Filled.ArrowForward"
            ToggledIcon="@Icons.Material.Filled.ArrowBack"
            Color="Color.Inherit" Edge="Edge.Start"/>
    </MudStack>
</MudDrawer>

<MudDrawer Anchor="Anchor.End" Style="top: 0" Width="22%" Class="mt-10 pt-4" Open="true"
           ClipMode="DrawerClipMode.Always"
           Elevation="2" Variant="@DrawerVariant.Persistent">
    <MudStack>
        <MudDrawerHeader>
            <MudStack>
                <MudStack Row>
                    <MudText Typo="Typo.h6">@PatientState.Patient?.FirstName @PatientState.Patient?.LastName</MudText>
                    <MudSpacer/>
                    <MudIconButton Icon="@Icons.Material.Filled.Edit" Variant="Variant.Filled" Color="Color.Primary"
                                   Href="@($"/patients/{PatientState.Patient?.Id}/edit")">
                        Edit
                    </MudIconButton>
                </MudStack>
                @if (PatientState.Patient?.Status != null)
                {
                    <MudChip T="string" Color="@PatientState.Patient.Status.GetColor()">
                        @PatientState.Patient.Status.GetDescription()
                    </MudChip>
                }
                <MudText Typo="Typo.body1">
                    @PatientState.Patient?.Gender.ToString(), @DateHelpers.GetYearsFromDate(PatientState.Patient?.BirthDate) y.o.
                </MudText>
            </MudStack>
        </MudDrawerHeader>
        <MudDivider/>
        <MudStack Class="ml-3">
            <div>
                <MudText Typo="Typo.body2">Contact Email</MudText>
                <MudText Typo="Typo.body1">@PatientState.Patient?.ContactEmail</MudText>
            </div>
            <div>
                <MudText Typo="Typo.body2">Contact Phone</MudText>
                <MudText Typo="Typo.body1">@PatientState.Patient?.ContactPhone</MudText>
            </div>
        </MudStack>

    </MudStack>
</MudDrawer>

@code {
    private bool _drawerOpen = true;

    protected override void OnInitialized()
    {
        PatientState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        PatientState.OnChange -= StateHasChanged;
    }

}