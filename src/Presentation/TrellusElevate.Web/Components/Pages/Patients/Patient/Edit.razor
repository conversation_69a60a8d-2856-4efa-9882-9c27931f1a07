@page "/patients/{PatientId:guid}/edit"
@using TrellusElevate.Core.Common.Enums
@using TrellusElevate.Presentation.Features.Patients.Mappers
@using TrellusElevate.Presentation.Features.Patients.Models
@using TrellusElevate.Web.Common.Helpers

@implements IDisposable

@inject IMediator Mediator
@inject ISnackbar Snackbar
@inject PatientState PatientState

<MudToolBar Dense="true" Class="mb-1" Gutters="true">
    <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" Href="/patients"/>
    <MudText Typo="Typo.h6">
        <MudIcon Icon="@Icons.Material.Filled.Person"/>
        Edit patient
    </MudText>
    <MudSpacer/>
    <MudStack Row="true" Spacing="2">
        <MudButton StartIcon="@Icons.Material.Filled.Save" Variant="Variant.Filled" Color="Color.Primary"
                   OnClick="Submit" Disabled="_loading">
            Save
        </MudButton>
    </MudStack>
</MudToolBar>

<MudDivider/>

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudForm Model="_request" @ref="_form" Validation="@_validator.AsMudFormValidation()" ValidationDelay="0">
    <MudGrid>
        <MudItem md="4">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Patient info</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudTextField  
                                  @bind-Value="_request.FirstName"
                                  For="@(() => _request.FirstName)"
                                  Immediate="true"
                                  Label="First Name *"/>
                    <MudTextField  
                                  @bind-Value="_request.LastName"
                                  For="@(() => _request.LastName)"
                                  Immediate="true"
                                  Label="Last Name *"/>

                    <MudStack Row>
                        <MudDatePicker Label="Birthdate *" ShowToolbar="false" For="@(() => _request.BirthDate)"
                                       
                                       
                                       @bind-Date="_request.BirthDate" Placeholder="Select a date"/>

                        <MudSelect   Dense="true"
                                   T="Gender?"
                                   Label="Sex *"
                                   @bind-Value="_request.Gender">
                            <MudSelectItem T="Gender?" Value="Gender.Male">
                                Male
                            </MudSelectItem>
                            <MudSelectItem T="Gender?" Value="Gender.Female">
                                Female
                            </MudSelectItem>
                        </MudSelect>
                    </MudStack>

                    <MudDivider/>

                    <MudTextField  
                                  @bind-Value="_request.ContactEmail"
                                  For="@(() => _request.ContactEmail)"
                                  InputType="InputType.Email"
                                  Immediate="true"
                                  Label="Personal email *"/>

                    <MudStack Row>
                        <MudTextField  
                                      @bind-Value="_request.ContactPhone"
                                      For="@(() => _request.ContactPhone)"
                                      InputType="InputType.Telephone"
                                      Immediate="true"
                                      Mask="@Masks.Phone"
                                      Adornment="Adornment.Start"
                                      AdornmentText="+1"
                                      Label="Primary Phone *"/>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</MudForm>

@code {
    [Parameter] public Guid PatientId { get; set; }

    private MudForm _form = null!;
    private bool _loading;

    private UpdatePatientRequest _request = new();

    private readonly UpdatePatientRequestValidator _validator = new();

    protected override async Task OnInitializedAsync()
    {
        PatientState.OnChange += StateHasChanged;
        var patient = await PatientState.LoadPatientAsync(PatientId);

        _request = new UpdatePatientRequest
        {
            FirstName = patient.FirstName,
            MiddleName = patient.MiddleName,
            LastName = patient.LastName,
            BirthDate = patient.BirthDate,
            Gender = patient.Gender,
            ContactEmail = patient.ContactEmail,
            ContactPhone = patient.ContactPhone
        };
    }

    public void Dispose()
    {
        PatientState.OnChange -= StateHasChanged;
    }

    private async Task Submit()
    {
        await _form.Validate();

        if (!_form.IsValid) return;

        _loading = true;

        var result = await Mediator.Send(_request.ToCommand(PatientState.PatientId!.Value));

        _loading = false;

        result.Switch(value =>
        {
            PatientState.SetPatient(value);
            Snackbar.Add($"Patient {value.FirstName} {value.LastName} is updated!", Severity.Success);
        }, error => Snackbar.Add($"Error saving patient: {error.Code}: {error.Description}", Severity.Error));
    }

}