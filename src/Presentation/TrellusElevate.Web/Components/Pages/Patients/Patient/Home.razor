@page "/patients/{PatientId:guid}"
@using TrellusElevate.Application.Features.Patients.Models
@implements IDisposable
@inject PatientState PatientState

@if (Patient == null!) return;

<PageTitle>Patient | @Patient.FullName</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Patients", href: "/patients", icon: Icons.Material.Filled.People),
                   new BreadcrumbItem(Patient.FullName, href: $"/patients/{PatientId}", icon: Icons.Material.Filled.Person)
               ])">
    </MudBreadcrumbs>
</MudToolBar>

<MudDivider/>

<MudGrid>
    <MudItem>
        
    </MudItem>
    <MudDivider Class="mt-3"/>
</MudGrid>


@code {
    [Parameter] public Guid PatientId { get; set; }
    
    private PatientDto Patient { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        PatientState.OnChange += StateHasChanged;
        Patient = await PatientState.LoadPatientAsync(PatientId);
    }

    public void Dispose()
    {
        PatientState.OnChange -= StateHasChanged;
    }
}
