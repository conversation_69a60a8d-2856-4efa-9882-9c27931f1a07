@using TrellusElevate.Application.Features.QuestionnaireResponses.Models
@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Core.Domains.Questionnaires.Common.Helpers
@using TrellusElevate.Core.Domains.Questionnaires.Entities
@using TrellusElevate.Core.Domains.Questionnaires.ValueObjects

@* Find the corresponding questionnaire item *@
@{
    var questionnaireItem = QuestionnaireItems.FirstOrDefault(q => q.Id == ResponseItem.QuestionnaireItemId);
}

@if (questionnaireItem != null)
{
    @* Handle Group type questions (containers) *@
    @if (questionnaireItem.Type == QuestionType.Group)
    {
        <MudCard Class="mb-3" Elevation="@(Level + 1)" Style="@($"margin-left: {Level * 20}px;")">
            <MudCardContent>
                @if (!string.IsNullOrWhiteSpace(questionnaireItem.Text))
                {
                    <MudText Typo="@(Level == 0 ? Typo.h6 : Typo.subtitle1)" Class="mb-3" Color="Color.Primary">
                        @if (!string.IsNullOrWhiteSpace(questionnaireItem.Prefix))
                        {
                            <strong>@questionnaireItem.Prefix</strong>
                        }
                        @questionnaireItem.Text
                    </MudText>
                }
                
                @* Render nested responses *@
                @if (ResponseItem.Items.Any())
                {
                    <MudStack Spacing="3">
                        @foreach (var childResponse in ResponseItem.Items)
                        {
                            <ResponseItemDisplay ResponseItem="childResponse" 
                                               QuestionnaireItems="QuestionnaireItems" 
                                               Level="@(Level + 1)" />
                        }
                    </MudStack>
                }
            </MudCardContent>
        </MudCard>
    }

    @* Handle Display type questions (informational) *@
    @if (questionnaireItem.Type == QuestionType.Display)
    {
        <MudAlert Severity="Severity.Info" Class="mb-3" Style="@($"margin-left: {Level * 20}px;")">
            @if (!string.IsNullOrWhiteSpace(questionnaireItem.Prefix))
            {
                <strong>@questionnaireItem.Prefix</strong>
            }
            @questionnaireItem.Text
        </MudAlert>
    }

    @* Handle answerable questions *@
    @if (questionnaireItem.Type.IsAnswerableType() && ResponseItem.Answers.Any())
    {
        <MudPaper Class="pa-3 mb-3" Elevation="1" Style="@($"margin-left: {Level * 20}px;")">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudText Typo="Typo.subtitle2" Color="Color.Primary">
                        @if (!string.IsNullOrWhiteSpace(questionnaireItem.Prefix))
                        {
                            <strong>@questionnaireItem.Prefix</strong>
                        }
                        @questionnaireItem.Text
                        @if (questionnaireItem.Required)
                        {
                            <MudText Typo="Typo.caption" Color="Color.Error" Style="display: inline;">*</MudText>
                        }
                    </MudText>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudStack Spacing="1">
                        @foreach (var answer in ResponseItem.Answers)
                        {
                            <MudChip T="string" 
                                     Variant="Variant.Filled" 
                                     Color="Color.Success" 
                                     Size="Size.Medium">
                                @FormatAnswer(answer, questionnaireItem)
                            </MudChip>
                        }
                    </MudStack>
                </MudItem>
            </MudGrid>
        </MudPaper>
    }

    @* Handle answerable questions without answers (skipped) *@
    @if (questionnaireItem.Type.IsAnswerableType() && !ResponseItem.Answers.Any())
    {
        <MudPaper Class="pa-3 mb-3" Elevation="1" Style="@($"margin-left: {Level * 20}px; opacity: 0.6;")">
            <MudGrid>
                <MudItem xs="12" md="6">
                    <MudText Typo="Typo.subtitle2" Color="Color.Secondary">
                        @if (!string.IsNullOrWhiteSpace(questionnaireItem.Prefix))
                        {
                            <strong>@questionnaireItem.Prefix</strong>
                        }
                        @questionnaireItem.Text
                        @if (questionnaireItem.Required)
                        {
                            <MudText Typo="Typo.caption" Color="Color.Error" Style="display: inline;">*</MudText>
                        }
                    </MudText>
                </MudItem>
                <MudItem xs="12" md="6">
                    <MudChip T="string" 
                              
                             Color="Color.Secondary" 
                             Size="Size.Medium">
                        No answer provided
                    </MudChip>
                </MudItem>
            </MudGrid>
        </MudPaper>
    }
}

@code {
    [Parameter] public QuestionnaireResponseItemDto ResponseItem { get; set; } = null!;
    [Parameter] public List<QuestionnaireItemDto> QuestionnaireItems { get; set; } = [];
    [Parameter] public int Level { get; set; } = 0;

    private string FormatAnswer(QuestionAnswer answer, QuestionnaireItemDto questionnaireItem)
    {
        if (answer.Value == null) return "No answer";

        // For questions with predefined options, show the option title
        if (questionnaireItem.Options.Any())
        {
            var option = questionnaireItem.Options.FirstOrDefault(o => 
                o.Answer != null && o.Answer.Equals(answer));
            if (option != null)
            {
                return option.Title ?? answer.Value.ToString() ?? "Unknown";
            }
        }

        // Format based on answer type
        return answer.Type switch
        {
            AnswerType.Boolean => (bool)answer.Value ? "Yes" : "No",
            AnswerType.Date => ((DateOnly)answer.Value).ToString("MMM dd, yyyy"),
            AnswerType.DateTime => ((DateTime)answer.Value).ToString("MMM dd, yyyy HH:mm"),
            AnswerType.Time => ((TimeOnly)answer.Value).ToString("HH:mm"),
            AnswerType.Decimal => ((decimal)answer.Value).ToString("F2"),
            AnswerType.Integer => answer.Value.ToString() ?? "0",
            AnswerType.String or AnswerType.Text or AnswerType.Url => answer.Value.ToString() ?? "",
            _ => answer.Value.ToString() ?? "Unknown"
        };
    }
}
