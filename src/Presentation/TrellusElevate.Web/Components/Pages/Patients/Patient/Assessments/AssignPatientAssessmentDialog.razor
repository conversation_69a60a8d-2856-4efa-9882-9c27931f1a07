@using TrellusElevate.Application.Features.Assessments.Models
@using TrellusElevate.Application.Features.Assessments.Queries
@using TrellusElevate.Application.Features.Patients.Models
@using TrellusElevate.Presentation.Features.Patients.Mappers
@using TrellusElevate.Presentation.Features.Patients.Models
@using Error = TrellusElevate.Core.Common.Errors.Error
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog!.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudAutocomplete   Dense="true" T="Guid"
                                 Label="Assessment"
                                 For="@(() => _request.AssessmentId)"
                                 @bind-Value="_request.AssessmentId"
                                 DebounceInterval="300"
                                 SearchFunc="@SearchAssessments"
                                 SelectOnActivation="false"
                                 ResetValueOnEmptyText="true"
                                 ShowProgressIndicator="true"
                                 ToStringFunc="@(c => _assessments.GetValueOrDefault(c)?.Name)"/>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Cancel</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Save
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public PatientDto Patient { get; set; } = null!;

    private MudForm? _form;
    private bool _loading;
    private Error? _error;

    private readonly CreatePatientAssessmentRequest _request = new();
    private readonly CreatePatientAssessmentRequestValidator _validator = new();

    private Dictionary<Guid, AssessmentDto> _assessments = new();

    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result = await Mediator.Send(_request.ToCommand(Patient.Id));

        result.Switch(value =>
        {
            Snackbar.Add($"Assessment {result.Value.Name} is assigned to patient", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();

    private async Task<IEnumerable<Guid>> SearchAssessments(string searchTerm, CancellationToken cancellationToken)
    {
        var result = await Mediator.Send(new GetAssessmentListQuery { SearchTerm = searchTerm }, cancellationToken);

        _assessments = result.Match(
            value => value.Items.ToDictionary(s => s.Id),
            error =>
            {
                _error = error;
                return new Dictionary<Guid, AssessmentDto>();
            });

        return _assessments.Keys;
    }

}