@page "/patients/{PatientId:guid}/assessments/{PatientAssessmentId:guid}/complete"
@using TrellusElevate.Application.Features.Patients.Commands
@using TrellusElevate.Application.Features.Patients.Models
@using TrellusElevate.Application.Features.Patients.Queries
@using TrellusElevate.Application.Features.QuestionnaireResponses.Models
@using TrellusElevate.Web.Components.Shared.Questionnaire
@implements IDisposable

@inject PatientState PatientState
@inject IMediator Mediator
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

@if (_patient == null! || _patientAssessment == null!)
{
    <MudProgressLinear Indeterminate="true" />
    return;
}

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Patients", href: "/patients", icon: Icons.Material.Filled.People),
                   new BreadcrumbItem(_patient.FullName, href: $"/patients/{PatientId}", icon: Icons.Material.Filled.Person),
                   new BreadcrumbItem("Assessments", href: $"/patients/{PatientId}/assessments", icon: Icons.Material.Filled.Assessment),
                   new BreadcrumbItem(_patientAssessment.Assessment!.Name!, href: $"/patients/{PatientId}/assessments/{PatientAssessmentId}/complete", icon: Icons.Material.Filled.Assessment),
               ])">
    </MudBreadcrumbs>
</MudToolBar>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudCard>
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h5">@_patientAssessment.Assessment!.Name</MudText>
                @if (!string.IsNullOrWhiteSpace(_patientAssessment.Assessment!.Description))
                {
                    <MudText Typo="Typo.body2" Color="Color.Secondary">@_patientAssessment.Assessment.Description</MudText>
                }
            </CardHeaderContent>
        </MudCardHeader>

        <MudCardContent>
            @if (_isLoading)
            {
                <MudProgressLinear Indeterminate="true" />
            }
            else
            {
                <QuestionnaireForm Questionnaire="@_patientAssessment.Assessment!.Questionnaire"
                                 @bind-QuestionnaireResponse="_questionnaireResponse"
                                 Disabled="@_isSaving"
                                 IsFormValidChanged="@OnFormValidChanged"
                                 OnAnswerChanged="@HandleAnswerChanged" />
            }
        </MudCardContent>

        <MudCardActions>
            <MudSpacer />
            <MudButton Variant="Variant.Text"
                       Color="Color.Secondary"
                       OnClick="@(() => Navigation.NavigateTo($"/patients/{PatientId}/assessments"))"
                       Disabled="@_isSaving">
                Cancel
            </MudButton>
            <MudButton Variant="Variant.Filled"
                       Color="Color.Primary"
                       OnClick="@SaveAssessment"
                       Disabled="@(_isSaving || !_isFormValid)"
                       StartIcon="@Icons.Material.Filled.Save">
                @if (_isSaving)
                {
                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                    <MudText Class="ml-2">Saving...</MudText>
                }
                else
                {
                    <MudText>Save Assessment</MudText>
                }
            </MudButton>
        </MudCardActions>
    </MudCard>
</MudContainer>

@code {
    [Parameter] public Guid PatientId { get; set; }
    [Parameter] public Guid PatientAssessmentId { get; set; }

    private PatientDto _patient = null!;
    private PatientAssessmentDto _patientAssessment = null!;
    private SaveQuestionnaireResponseDto _questionnaireResponse = null!;

    private bool _isFormValid;
    private bool _isLoading = true;
    private bool _isSaving;

    protected override async Task OnInitializedAsync()
    {
        PatientState.OnChange += StateHasChanged;

        try
        {
            _patient = await PatientState.LoadPatientAsync(PatientId);

            var result = await Mediator.Send(new GetPatientAssessmentByIdQuery(PatientId, PatientAssessmentId));

            _patientAssessment = result.Match(
                value => value,
                error =>
                {
                    Snackbar.Add($"Error loading patient assessment: {error.Code}: {error.Description}", Severity.Error);
                    return new PatientAssessmentDto();
                });
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Unexpected error: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
        }
    }


    private async Task SaveAssessment()
    {
        if (_isSaving || !_isFormValid) return;

        _isSaving = true;
        StateHasChanged();

        try
        {
            var command = new CompletePatientAssessmentCommand(PatientId, PatientAssessmentId, _questionnaireResponse);
            var result = await Mediator.Send(command);

            result.Match(
                success =>
                {
                    Snackbar.Add("Assessment completed successfully!", Severity.Success);
                    Navigation.NavigateTo($"/patients/{PatientId}/assessments");
                    return success;
                },
                error =>
                {
                    Snackbar.Add($"Error saving assessment: {error.Code}: {error.Description}", Severity.Error);
                    return new PatientAssessmentDto();
                });
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Unexpected error while saving: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isSaving = false;
            StateHasChanged();
        }
    }

    private Task HandleAnswerChanged()
    {
        // Force re-render of all components to re-evaluate EnableWhen conditions
        // The OnParametersSet lifecycle method in QuestionItemControl will handle visibility updates
        InvokeAsync(StateHasChanged);
        return Task.CompletedTask;
    }



    private Task OnFormValidChanged(bool isValid)
    {
        _isFormValid = isValid;
        return Task.CompletedTask;
    }

    public void Dispose()
    {
        PatientState.OnChange -= StateHasChanged;
    }
}