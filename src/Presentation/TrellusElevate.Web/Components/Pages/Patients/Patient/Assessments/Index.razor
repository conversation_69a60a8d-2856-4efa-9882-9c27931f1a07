@page "/patients/{PatientId:guid}/assessments"
@using TrellusElevate.Application.Features.Patients.Models
@using TrellusElevate.Application.Features.Patients.Queries
@using TrellusElevate.Application.Features.Patients.Commands
@using TrellusElevate.Core.Domains.Patients.Entities
@using TrellusElevate.Web.Common.Extensions

@implements IDisposable

@inject PatientState PatientState
@inject IMediator Mediator
@inject ISnackbar Snackbar
@inject IDialogService DialogService

@if (_patient == null!) return;

<PageTitle>Patient | Asessments</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Patients", href: "/patients", icon: Icons.Material.Filled.People),
                   new BreadcrumbItem(_patient.FullName, href: $"/patients/{PatientId}", icon: Icons.Material.Filled.Person),
                   new BreadcrumbItem("Assessments", href: $"/patients/{PatientId}/assessments", icon: Icons.Material.Filled.Assessment),
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="CreateAsync" Variant="Variant.Filled"
               Color="Color.Primary">
        New
    </MudButton>
</MudToolBar>

<MudDataGrid Items="@_assessments" T="PatientAssessmentListItemDto" Dense="true" Striped="true"
             SortMode="SortMode.Single" QuickFilter="QuickFilter">
    <ToolBarContent>

        <MudStack Row Spacing="2" AlignItems="AlignItems.Start" Wrap="Wrap.Wrap">

            <MudSelect Dense="true"
                       T="string"
                       Clearable="true"
                       Label="Assessment"
                       MultiSelection="true"
                       @bind-SelectedValues="_filterAssessments">
                @foreach (var item in _assessmentNames)
                {
                    <MudSelectItem T="string" Value="item">
                        @item
                    </MudSelectItem>
                }
            </MudSelect>

            <MudSelect Dense="true"
                       T="PatientAssessmentStatus"
                       Clearable="true"
                       Label="Status"
                       MultiSelection="true"
                       MultiSelectionTextFunc="@(list => string.Join(",", list!.Select(s => Enum.Parse<PatientAssessmentStatus>(s!).ToString())))"
                       @bind-SelectedValues="_filterStatus">
                @foreach (var item in Enum.GetValues<PatientAssessmentStatus>())
                {
                    <MudSelectItem T="PatientAssessmentStatus" Value="item">
                        @item.ToString()
                    </MudSelectItem>
                }
            </MudSelect>
        </MudStack>
    </ToolBarContent>
    <Columns>
        <PropertyColumn Property="x => x.Name" Title="Name"/>
        <PropertyColumn Property="x => x.Status" Title="Status">
            <CellTemplate>
                <MudChip T="string" Color="@context.Item.Status.GetColor()">
                    @context.Item.Status.ToString()
                </MudChip>
            </CellTemplate>
        </PropertyColumn>
        <TemplateColumn Title="Scores">
            <CellTemplate>
                @if (context.Item.Scores.Any())
                {
                    <MudStack Spacing="1">
                        @foreach (var score in context.Item.Scores)
                        {
                            <MudTooltip Text="@($"{score.AssessmentScoringProfile?.Name}: {score.Value:F2}")">
                                <MudChip T="string"
                                         Size="Size.Small"
                                         Variant="Variant.Filled"
                                         Color="Color.Info">
                                    @($"{score.AssessmentScoringProfile?.Name}: {score.Value:F2}")
                                </MudChip>
                            </MudTooltip>
                        }
                    </MudStack>
                }
                else
                {
                    <MudText Typo="Typo.caption" Color="Color.Secondary">-</MudText>
                }
            </CellTemplate>
        </TemplateColumn>
        <PropertyColumn Property="x => x.CreatedAt" Title="Created at" Format="MMM dd, yyyy 'at' HH:mm"/>
        <PropertyColumn Property="x => x.CompletedAt" Title="Completed at" Format="MMM dd, yyyy 'at' HH:mm"/>
        <TemplateColumn Title="Actions">
            <CellTemplate>
                <MudStack Row="true" Spacing="2">
                    @if (context.Item!.Status == PatientAssessmentStatus.Pending)
                    {
                        <MudButton StartIcon="@Icons.Material.Filled.Edit"
                                   Href="@($"/patients/{PatientId}/assessments/{context.Item.Id}/complete")"
                                   Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   Size="Size.Small">
                            Complete
                        </MudButton>

                        <MudButton StartIcon="@Icons.Material.Filled.SkipNext"
                                   OnClick="@(() => SkipAssessmentAsync(context.Item))"
                                   Variant="Variant.Outlined"
                                   Color="Color.Warning"
                                   Size="Size.Small">
                            Skip
                        </MudButton>

                        <MudButton StartIcon="@Icons.Material.Filled.Cancel"
                                   OnClick="@(() => CancelAssessmentAsync(context.Item))"
                                   Variant="Variant.Outlined"
                                   Color="Color.Error"
                                   Size="Size.Small">
                            Cancel
                        </MudButton>
                    }
                    @if (context.Item.Status == PatientAssessmentStatus.Completed)
                    {
                        <MudButton StartIcon="@Icons.Material.Filled.Visibility"
                                   Href="@($"/patients/{PatientId}/assessments/{context.Item.Id}/details")"

                                   Color="Color.Info"
                                   Size="Size.Small">
                            View Details
                        </MudButton>
                    }
                </MudStack>
            </CellTemplate>
        </TemplateColumn>
    </Columns>
    <PagerContent>
        <MudDataGridPager T="PatientAssessmentListItemDto"/>
    </PagerContent>
</MudDataGrid>

@code {
    [Parameter] public Guid PatientId { get; set; }

    private PatientDto _patient = null!;

    private List<PatientAssessmentListItemDto> _assessments = [];
    private List<string> _assessmentNames = [];

    private IEnumerable<PatientAssessmentStatus>? _filterStatus;
    private IEnumerable<string?>? _filterAssessments;

    protected override async Task OnInitializedAsync()
    {
        PatientState.OnChange += StateHasChanged;
        _patient = await PatientState.LoadPatientAsync(PatientId);

        var result = await Mediator.Send(new GetPatientAssessmentListQuery(PatientId));

        _assessments = result.Match(
            value =>
            {
                _assessmentNames = value.Items.Select(s => s.Name).Distinct().ToList();
                return value.Items;
            },
            error =>
            {
                Snackbar.Add($"Error loading patient assessments: {error.Code}: {error.Description}", Severity.Error);
                return [];
            });
    }

    public void Dispose()
    {
        PatientState.OnChange -= StateHasChanged;
    }

    private Func<PatientAssessmentListItemDto, bool> QuickFilter => x
        => (_filterStatus == null || !_filterStatus.Any() || _filterStatus.Contains(x.Status))
           && (_filterAssessments == null || !_filterAssessments.Any() || _filterAssessments.Contains(x.Name));

    private async Task CreateAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<AssignPatientAssessmentDialog> { { x => x.Patient, _patient } };

        var dialog = await DialogService.ShowAsync<AssignPatientAssessmentDialog>("Assign assessment", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            _assessments.Add((result.Data as PatientAssessmentListItemDto)!);
        }
    }

    private async Task SkipAssessmentAsync(PatientAssessmentListItemDto assessment)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "Skip Assessment",
            $"Are you sure you want to skip the assessment '{assessment.Name}'? This action cannot be undone.",
            yesText: "Skip", cancelText: "Cancel");

        if (confirmed == true)
        {
            var result = await Mediator.Send(new SkipPatientAssessmentCommand(PatientId, assessment.Id));

            result.Switch(
                value =>
                {
                    var index = _assessments.FindIndex(a => a.Id == assessment.Id);
                    if (index >= 0)
                    {
                        _assessments[index].Status = PatientAssessmentStatus.Skipped;
                        StateHasChanged();
                    }

                    Snackbar.Add($"Assessment '{assessment.Name}' has been skipped.", Severity.Success);
                },
                error => Snackbar.Add($"Error skipping assessment: {error.Code}: {error.Description}", Severity.Error));
        }
    }

    private async Task CancelAssessmentAsync(PatientAssessmentListItemDto assessment)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "Cancel Assessment",
            $"Are you sure you want to cancel the assessment '{assessment.Name}'? This action cannot be undone.",
            yesText: "Cancel Assessment", cancelText: "Keep Assessment");

        if (confirmed == true)
        {
            var result = await Mediator.Send(new CancelPatientAssessmentCommand(PatientId, assessment.Id));

            result.Switch(
                value =>
                {
                    var index = _assessments.FindIndex(a => a.Id == assessment.Id);
                    if (index >= 0)
                    {
                        _assessments[index].Status = PatientAssessmentStatus.Cancelled;
                        StateHasChanged();
                    }

                    Snackbar.Add($"Assessment '{assessment.Name}' has been cancelled.", Severity.Success);
                },
                error => Snackbar.Add($"Error cancelling assessment: {error.Code}: {error.Description}", Severity.Error));
        }
    }

}