@page "/patients/{PatientId:guid}/assessments/{PatientAssessmentId:guid}/details"
@using TrellusElevate.Application.Common.Extensions
@using TrellusElevate.Application.Features.Patients.Models
@using TrellusElevate.Application.Features.Patients.Queries
@using TrellusElevate.Application.Features.Questionnaires.Models

@implements IDisposable

@inject PatientState PatientState
@inject IMediator Mediator
@inject ISnackbar Snackbar

@if (_patient == null! || _patientAssessment == null!)
{
    <MudProgressLinear Indeterminate="true" />
    return;
}

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Patients", href: "/patients", icon: Icons.Material.Filled.People),
                   new BreadcrumbItem(_patient.FullName, href: $"/patients/{PatientId}", icon: Icons.Material.Filled.Person),
                   new BreadcrumbItem("Assessments", href: $"/patients/{PatientId}/assessments", icon: Icons.Material.Filled.Assessment),
                   new BreadcrumbItem($"{_patientAssessment.Assessment?.Name}", href: $"/patients/{PatientId}/assessments/{PatientAssessmentId}/details", icon: Icons.Material.Filled.Assessment),
               ])">
    </MudBreadcrumbs>
</MudToolBar>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    @if (_isLoading)
    {
        <MudProgressLinear Indeterminate="true" />
    }
    else
    {
        @* Assessment Header *@
        <MudCard Class="mb-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudStack Row="true" AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                        <div>
                            <MudText Typo="Typo.h4">@_patientAssessment.Assessment!.Name</MudText>
                            @if (!string.IsNullOrWhiteSpace(_patientAssessment.Assessment.Description))
                            {
                                <MudText Typo="Typo.body1" Color="Color.Secondary">@_patientAssessment.Assessment.Description</MudText>
                            }
                        </div>
                        <div>
                            <MudText Typo="Typo.subtitle2" Color="Color.Primary">Completed At</MudText>
                            <MudText Typo="Typo.body1">
                                @(_patientAssessment.CompletedAt?.ToString("MMM dd, yyyy 'at' HH:mm") ?? "Not completed")
                            </MudText>
                        </div>
                    </MudStack>
                </CardHeaderContent>
            </MudCardHeader>
        </MudCard>

        @* Assessment Scores *@
        @if (_patientAssessment.AssessmentResponse?.Scores.Any() == true)
        {
            <MudCard Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h5">
                            <MudIcon Icon="@Icons.Material.Filled.Analytics" Class="mr-2" />
                            Assessment Scores
                        </MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        @foreach (var score in _patientAssessment.AssessmentResponse.Scores)
                        {
                            <MudItem xs="12" sm="6" md="4">
                                <MudPaper Class="pa-4" Elevation="2">
                                    <MudStack Spacing="2">
                                        <MudText Typo="Typo.subtitle1" Color="Color.Primary">
                                            @score.AssessmentScoringProfile?.Name
                                        </MudText>
                                        <MudText Typo="Typo.h4" Color="Color.Info">
                                            @score.Value
                                        </MudText>
                                        @if (!string.IsNullOrWhiteSpace(score.AssessmentScoringProfile?.Description))
                                        {
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                @score.AssessmentScoringProfile.Description
                                            </MudText>
                                        }
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                        }
                    </MudGrid>
                </MudCardContent>
            </MudCard>
        }

        @* Assessment Response *@
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h5">
                        <MudIcon Icon="@Icons.Material.Filled.Quiz" Class="mr-2" />
                        Assessment Response
                    </MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                @if (_patientAssessment.AssessmentResponse?.QuestionnaireResponse?.Items.Any() == true)
                {
                    <MudStack Spacing="4">
                        @foreach (var responseItem in _patientAssessment.AssessmentResponse.QuestionnaireResponse.Items)
                        {
                            <ResponseItemDisplay ResponseItem="responseItem" 
                                               QuestionnaireItems="_allQuestionnaireItems" 
                                               Level="0" />
                        }
                    </MudStack>
                }
                else
                {
                    <MudAlert Severity="Severity.Info">
                        No responses found for this assessment.
                    </MudAlert>
                }
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    [Parameter] public Guid PatientId { get; set; }
    [Parameter] public Guid PatientAssessmentId { get; set; }

    private PatientDto _patient = null!;
    private PatientAssessmentDto _patientAssessment = null!;
    private List<QuestionnaireItemDto> _allQuestionnaireItems = [];
    private bool _isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        PatientState.OnChange += StateHasChanged;
        
        try
        {
            _patient = await PatientState.LoadPatientAsync(PatientId);

            var result = await Mediator.Send(new GetPatientAssessmentByIdQuery(PatientId, PatientAssessmentId));

            _patientAssessment = result.Match(
                value =>
                {
                    // Flatten all questionnaire items for display
                    if (value.Assessment?.Questionnaire?.Items != null)
                    {
                        _allQuestionnaireItems = value.Assessment.Questionnaire.Items
                            .Flatten(s => s.Items)
                            .ToList();
                    }
                    
                    return value;
                },
                error =>
                {
                    Snackbar.Add($"Error loading patient assessment: {error.Code}: {error.Description}", Severity.Error);
                    return new PatientAssessmentDto();
                });
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Unexpected error: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
        }
    }

    public void Dispose()
    {
        PatientState.OnChange -= StateHasChanged;
    }
}
