@page "/404"
@layout Layout.EmptyLayout
@inject IJSRuntime JSRuntime

<div class="not-found-container" id="notFoundContainer">
    <div class="not-found-content" id="notFoundContent">
        <div class="error-code">
            <span>4</span>
            <div class="planet-container">
                <div class="planet"></div>
            </div>
            <span>4</span>
        </div>

        <h2 class="subtitle">Lost in Space</h2>
        <p class="description">The cosmic coordinates you were looking for couldn't be located in this universe.</p>

        <a href="/" class="return-button" id="returnButton">
            <span class="button-text">Return Home</span>
            <span class="button-icon">→</span>
        </a>
    </div>

    <div class="astronaut" id="astronaut">
        <svg viewBox="0 0 100 125" width="100" height="125">
            <!-- Helmet -->
            <ellipse cx="50" cy="40" rx="22" ry="22" fill="#f8f9fa" stroke="#dedede" stroke-width="1" />
            <!-- Visor -->
            <ellipse cx="50" cy="38" rx="15" ry="16" fill="#4facfe" stroke="#dedede" stroke-width="0.5" opacity="0.7" />
            <!-- Body -->
            <path d="M35 62 v-20 a15,15 0 0,1 30,0 v20 a15,20 0 0,1 -30,0 z" fill="#f8f9fa" stroke="#dedede" stroke-width="1" />
            <!-- Backpack -->
            <rect x="40" y="62" width="20" height="15" rx="2" fill="#e9ecef" stroke="#dedede" stroke-width="1" />
            <!-- Arms -->
            <path d="M36 50 Q25 55 28 70" fill="none" stroke="#f8f9fa" stroke-width="5" stroke-linecap="round" />
            <path d="M64 50 Q75 55 72 70" fill="none" stroke="#f8f9fa" stroke-width="5" stroke-linecap="round" />
            <!-- Legs -->
            <path d="M40 85 Q45 77 50 85" fill="none" stroke="#f8f9fa" stroke-width="6" stroke-linecap="round" />
            <path d="M60 85 Q55 77 50 85" fill="none" stroke="#f8f9fa" stroke-width="6" stroke-linecap="round" />
            <!-- Reflection on helmet -->
            <path d="M45 30 Q50 25 55 30" fill="none" stroke="rgba(255,255,255,0.6)" stroke-width="1" />
        </svg>
    </div>
</div>

<style>
    /* Base styles */
    .not-found-container {
        min-height: 100vh;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #0f0c29;
        background: linear-gradient(135deg, #0f0c29 0%, #302b63 50%, #24243e 100%);
        color: white;
        font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
        position: relative;
        overflow: hidden;
    }

    /* Content styles */
    .not-found-content {
        text-align: center;
        max-width: 700px;
        padding: 2rem;
        position: relative;
        z-index: 10;
        opacity: 0;
    }

    .error-code {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 9rem;
        font-weight: 800;
        margin: 0;
        color: #ffffff;
        letter-spacing: -0.05em;
    }

    .planet-container {
        width: 100px;
        height: 100px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 10px;
    }

    .planet {
        position: relative;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: #4facfe;
        box-shadow: 0 0 30px rgba(79, 172, 254, 0.5);
    }

    .subtitle {
        font-size: 2rem;
        margin: 2rem 0 0.5rem;
        font-weight: 300;
        color: #eef2f3;
    }

    .description {
        font-size: 1.125rem;
        margin-bottom: 2.5rem;
        color: #b8c6db;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
    }

    .return-button {
        display: inline-flex;
        align-items: center;
        background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        text-decoration: none;
        padding: 0.875rem 2rem;
        border-radius: 50px;
        font-weight: 500;
        font-size: 1rem;
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        position: relative;
        overflow: hidden;
    }

    .button-text {
        position: relative;
        z-index: 2;
    }

    .button-icon {
        margin-left: 0.625rem;
        position: relative;
        z-index: 2;
    }

    .astronaut {
        position: absolute;
        right: 15%;
        bottom: 15%;
        z-index: 10;
        cursor: pointer;
    }
</style>

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Add stars to background
            await JSRuntime.InvokeVoidAsync("createStars", "notFoundContainer");

            // Add animations without keyframes
            await JSRuntime.InvokeVoidAsync("setupAnimations", "notFoundContent", "astronaut", "returnButton");

            // Add responsive adjustments
            await JSRuntime.InvokeVoidAsync("setupResponsive");

            // Add astronaut escape behavior
            await JSRuntime.InvokeVoidAsync("setupAstronautEscape", "astronaut", "notFoundContainer");
        }
    }
}

<script>
    window.createStars = function(containerId) {
        var container = document.getElementById(containerId);

        // Create stars
        for (var i = 0; i < 100; i++) {
            var star = document.createElement('div');
            star.style.position = 'absolute';
            star.style.width = Math.random() * 3 + 'px';
            star.style.height = star.style.width;
            star.style.backgroundColor = 'white';
            star.style.borderRadius = '50%';
            star.style.opacity = Math.random() * 0.8 + 0.2;
            star.style.top = Math.random() * 100 + '%';
            star.style.left = Math.random() * 100 + '%';
            star.style.zIndex = '1';
            container.appendChild(star);

            // Simple twinkle effect
            setInterval(function(star) {
                return function() {
                    star.style.opacity = Math.random() * 0.8 + 0.2;
                }
            }(star), Math.random() * 3000 + 2000);
        }

        // Create shooting stars
        for (var i = 0; i < 2; i++) {
            createShootingStar(container);
        }
    };

    function createShootingStar(container) {
        var star = document.createElement('div');
        star.style.position = 'absolute';
        star.style.width = '2px';
        star.style.height = '2px';
        star.style.backgroundColor = 'white';
        star.style.boxShadow = '0 0 10px 2px white';
        star.style.borderRadius = '50%';
        star.style.top = Math.random() * 30 + '%';
        star.style.left = Math.random() * 30 + '%';
        star.style.zIndex = '1';
        container.appendChild(star);

        animateShootingStar(star);
    }

    function animateShootingStar(star) {
        var startX = parseFloat(star.style.left);
        var startY = parseFloat(star.style.top);
        var duration = Math.random() * 3000 + 2000;

        var startTime = null;

        function animate(timestamp) {
            if (!startTime) startTime = timestamp;
            var progress = (timestamp - startTime) / duration;

            if (progress < 1) {
                var x = startX + (progress * 70);
                var y = startY + (progress * 70);

                star.style.left = x + '%';
                star.style.top = y + '%';
                star.style.opacity = 1 - progress;

                requestAnimationFrame(animate);
            } else {
                star.style.left = startX + '%';
                star.style.top = startY + '%';
                star.style.opacity = 1;

                setTimeout(function() {
                    animateShootingStar(star);
                }, Math.random() * 5000 + 2000);
            }
        }

        requestAnimationFrame(animate);
    }

    window.setupAnimations = function(contentId, astronautId, buttonId) {
        // Fade in content
        var content = document.getElementById(contentId);
        content.style.transition = 'opacity 1.5s ease, transform 1.5s ease';
        content.style.transform = 'translateY(20px)';

        setTimeout(function() {
            content.style.opacity = '1';
            content.style.transform = 'translateY(0)';
        }, 100);

        // Float astronaut
        var astronaut = document.getElementById(astronautId);
        var floatUp = true;
        var baseY = 0;

        window.astronautFloatInterval = setInterval(function() {
            if (floatUp) {
                baseY -= 0.2;
                if (baseY <= -20) floatUp = false;
            } else {
                baseY += 0.2;
                if (baseY >= 0) floatUp = true;
            }

            // Store the base Y position for the escape function to use
            astronaut.baseY = baseY;

            // Only apply if not currently escaping
            if (!astronaut.isEscaping) {
                astronaut.style.transform = 'translateY(' + baseY + 'px)';
            }
        }, 50);

        // Planet glow effect
        var planets = document.getElementsByClassName('planet');
        var glowUp = true;
        var baseGlow = 0.5;

        setInterval(function() {
            if (glowUp) {
                baseGlow += 0.01;
                if (baseGlow >= 0.8) glowUp = false;
            } else {
                baseGlow -= 0.01;
                if (baseGlow <= 0.5) glowUp = true;
            }

            for (var i = 0; i < planets.length; i++) {
                planets[i].style.boxShadow = '0 0 ' + (30 + (baseGlow * 20)) + 'px rgba(79, 172, 254, ' + baseGlow + ')';
            }
        }, 50);

        // Button hover effect
        var button = document.getElementById(buttonId);
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 12px 28px rgba(79, 172, 254, 0.4)';
            this.querySelector('.button-text').style.transform = 'translateX(-3px)';
            this.querySelector('.button-icon').style.transform = 'translateX(3px)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 8px 20px rgba(79, 172, 254, 0.3)';
            this.querySelector('.button-text').style.transform = 'translateX(0)';
            this.querySelector('.button-icon').style.transform = 'translateX(0)';
        });

        // Add transition styles programmatically
        button.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
        button.querySelector('.button-text').style.transition = 'transform 0.3s ease';
        button.querySelector('.button-icon').style.transition = 'transform 0.3s ease';
    };

    window.setupResponsive = function() {
        function adjustSizes() {
            var width = window.innerWidth;
            var errorCode = document.querySelector('.error-code');
            var planetContainer = document.querySelector('.planet-container');
            var planet = document.querySelector('.planet');
            var subtitle = document.querySelector('.subtitle');
            var astronaut = document.getElementById('astronaut');

            if (width <= 768) {
                errorCode.style.fontSize = '6rem';
                planetContainer.style.width = '70px';
                planetContainer.style.height = '70px';
                planet.style.width = '60px';
                planet.style.height = '60px';
                subtitle.style.fontSize = '1.75rem';

                if (astronaut) {
                    astronaut.style.right = '5%';
                    astronaut.style.bottom = '10%';
                    astronaut.style.width = '80px';
                    astronaut.style.height = '100px';
                }
            }

            if (width <= 480) {
                errorCode.style.fontSize = '4rem';
                planetContainer.style.width = '50px';
                planetContainer.style.height = '50px';
                planet.style.width = '40px';
                planet.style.height = '40px';
                subtitle.style.fontSize = '1.5rem';

                if (astronaut) {
                    // Don't hide the astronaut completely for small screens anymore
                    // since we added the interactive escape feature
                    astronaut.style.width = '60px';
                    astronaut.style.height = '75px';
                    astronaut.style.right = '10%';
                    astronaut.style.bottom = '5%';
                }
            }
        }

        // Initial adjustment
        adjustSizes();

        // Adjust on window resize
        window.addEventListener('resize', adjustSizes);
    };

    window.setupAstronautEscape = function(astronautId, containerId) {
        var astronaut = document.getElementById(astronautId);
        var container = document.getElementById(containerId);
        var containerRect = container.getBoundingClientRect();

        // Store original position
        var computedStyle = window.getComputedStyle(astronaut);
        astronaut.origRight = parseFloat(computedStyle.right);
        astronaut.origBottom = parseFloat(computedStyle.bottom);
        astronaut.isEscaping = false;
        astronaut.escapeVelocityX = 0;
        astronaut.escapeVelocityY = 0;

        // Add transition for smoother movement
        astronaut.style.transition = 'transform 0.1s ease';

        // Track the mouse position
        var mouseX = 0;
        var mouseY = 0;

        container.addEventListener('mousemove', function(e) {
            mouseX = e.clientX;
            mouseY = e.clientY;

            // Check if mouse is near astronaut
            var astronautRect = astronaut.getBoundingClientRect();
            var astronautCenterX = astronautRect.left + astronautRect.width / 2;
            var astronautCenterY = astronautRect.top + astronautRect.height / 2;

            var distance = Math.sqrt(
                Math.pow(mouseX - astronautCenterX, 2) +
                Math.pow(mouseY - astronautCenterY, 2)
            );

            // If mouse gets close to astronaut, make it escape
            if (distance < 150 && !astronaut.isEscaping) {
                startEscape(mouseX, mouseY, astronautCenterX, astronautCenterY);
            }
        });

        function startEscape(mouseX, mouseY, astronautX, astronautY) {
            // Calculate escape angle (opposite direction from mouse)
            var angleRad = Math.atan2(astronautY - mouseY, astronautX - mouseX);

            // Set velocities based on angle
            var escapeSpeed = 12;
            astronaut.escapeVelocityX = Math.cos(angleRad) * escapeSpeed;
            astronaut.escapeVelocityY = Math.sin(angleRad) * escapeSpeed;

            // Set flag to escape
            astronaut.isEscaping = true;

            // Add some rotation for fun
            var rotationAmount = (Math.random() * 40) - 20; // -20 to +20 degrees

            // Start escape animation
            var escapeX = 0;
            var escapeY = 0;

            var escapeDuration = 60; // frames
            var escapeProgress = 0;

            // Apply a starting "boost" effect - astronaut spins and moves quickly at first
            astronaut.style.transition = 'transform 0.2s cubic-bezier(0.05, 0.95, 0.65, 1.35)';
            astronaut.style.transform = 'translateX(' + (escapeX + astronaut.escapeVelocityX * 2) + 'px) ' +
                'translateY(' + (escapeY + astronaut.escapeVelocityY * 2 + astronaut.baseY) + 'px) ' +
                'rotate(' + rotationAmount + 'deg)';

            // Create subtle animation for thruster effect
            var thruster = document.createElement('div');
            thruster.style.position = 'absolute';
            thruster.style.width = '12px';
            thruster.style.height = '25px';
            thruster.style.background = 'linear-gradient(to bottom, rgba(79, 172, 254, 0.8), rgba(79, 172, 254, 0))';
            thruster.style.borderRadius = '50%';
            thruster.style.pointerEvents = 'none';
            thruster.style.zIndex = '9';
            astronaut.parentNode.appendChild(thruster);

            var escapeInterval = setInterval(function() {
                escapeProgress++;

                // Calculate new position with easing
                escapeX += astronaut.escapeVelocityX * (1 - (escapeProgress / escapeDuration) * 0.8);
                escapeY += astronaut.escapeVelocityY * (1 - (escapeProgress / escapeDuration) * 0.8);

                // Apply the transform
                astronaut.style.transition = 'transform 0.1s ease';
                astronaut.style.transform = 'translateX(' + escapeX + 'px) ' +
                    'translateY(' + (escapeY + astronaut.baseY) + 'px) ' +
                    'rotate(' + (rotationAmount * (1 - escapeProgress / escapeDuration)) + 'deg)';

                // Position the thruster behind the astronaut in the opposite direction of movement
                var astronautRect = astronaut.getBoundingClientRect();
                var thrusterAngle = Math.atan2(astronaut.escapeVelocityY, astronaut.escapeVelocityX) + Math.PI;
                var thrusterX = astronautRect.left + astronautRect.width/2 + Math.cos(thrusterAngle) * astronautRect.width/2;
                var thrusterY = astronautRect.top + astronautRect.height/2 + Math.sin(thrusterAngle) * astronautRect.height/2;

                thruster.style.left = (thrusterX - thruster.offsetWidth/2) + 'px';
                thruster.style.top = (thrusterY - thruster.offsetHeight/2) + 'px';
                thruster.style.transform = 'rotate(' + (thrusterAngle + Math.PI/2) * 180 / Math.PI + 'deg)';
                thruster.style.opacity = (1 - escapeProgress / escapeDuration) * 0.8;

                // Check if we should keep escaping
                if (escapeProgress >= escapeDuration) {
                    clearInterval(escapeInterval);

                    // Reset position and end escape
                    setTimeout(function() {
                        astronaut.style.transition = 'transform 1s cubic-bezier(0.34, 1.56, 0.64, 1)';
                        astronaut.style.transform = 'translateY(' + astronaut.baseY + 'px) rotate(0deg)';
                        astronaut.isEscaping = false;

                        // Remove thruster
                        thruster.remove();
                    }, 100);
                }
            }, 16); // ~60fps
        }
    };
</script>