@page "/administration"
@inject NavigationManager NavigationManager

<PageTitle>Administration</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-6">

    <MudText Typo="Typo.h4" GutterBottom="true" Align="Align.Center">Administration Panel</MudText>

    <MudText Typo="Typo.h5" Class="mt-6 mb-3">General</MudText>
    <MudGrid Justify="Justify.FlexStart" Spacing="3">
        <MudItem xs="12" sm="6" md="4" lg="3">
            <MudCard Elevation="3" Class="admin-card" @onclick='() => NavigateTo("administration/questionnaires")'>
                <MudCardContent Class="d-flex flex-column align-center text-center">
                    <MudIcon Icon="@Icons.Material.Filled.QuestionMark" Size="Size.Large" Color="Color.Primary" Class="mb-3" />
                    <MudText Typo="Typo.h6" GutterBottom="true">Questionnaires</MudText>
                    <MudText Typo="Typo.body2">Manage survey questions and templates.</MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <MudDivider Class="my-6" />

    <MudText Typo="Typo.h5" Class="mt-6 mb-3">Assessments</MudText>
    <MudGrid Justify="Justify.FlexStart" Spacing="3">
        <MudItem xs="12" sm="6" md="4" lg="3">
            <MudCard Elevation="3" Class="admin-card" @onclick='() => NavigateTo("administration/assessments")'>
                <MudCardContent Class="d-flex flex-column align-center text-center">
                    <MudIcon Icon="@Icons.Material.Filled.Assessment" Size="Size.Large" Color="Color.Secondary" Class="mb-3" />
                    <MudText Typo="Typo.h6" GutterBottom="true">Assessments</MudText>
                    <MudText Typo="Typo.body2">Manage assessments configuration</MudText>
                </MudCardContent>
            </MudCard>
        </MudItem>
   </MudGrid>

    <MudDivider Class="my-6" />

    <MudText Typo="Typo.h5" Class="mt-6 mb-3">Lessons Management</MudText>
    <MudGrid Justify="Justify.FlexStart" Spacing="3">
        <MudItem xs="12">
             <MudText Typo="Typo.body2" Color="Color.Dark"><i>No items in this section yet.</i></MudText>
        </MudItem>
    </MudGrid>

    <MudDivider Class="my-6" />

    <MudText Typo="Typo.h5" Class="mt-6 mb-3">Medications</MudText>
     <MudGrid Justify="Justify.FlexStart" Spacing="3">
         <MudItem xs="12">
             <MudText Typo="Typo.body2" Color="Color.Dark"><i>No items in this section yet.</i></MudText>
        </MudItem>
    </MudGrid>

    <MudDivider Class="my-6" />

    <MudText Typo="Typo.h5" Class="mt-6 mb-3">Encounters</MudText>
    <MudGrid Justify="Justify.FlexStart" Spacing="3">
        <MudItem xs="12">
             <MudText Typo="Typo.body2" Color="Color.Dark"><i>No items in this section yet.</i></MudText>
        </MudItem>
    </MudGrid>

</MudContainer>

<style>
    .admin-card {
        cursor: pointer;
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        display: flex;
        flex-direction: column;
    }

    .admin-card .mud-card-content {
        flex-grow: 1;
        padding: 16px;
    }

    .admin-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 6px 16px rgba(0,0,0,0.1);
    }
</style>

@code {
    private void NavigateTo(string url)
    {
        NavigationManager.NavigateTo(url);
    }
}
