@using TrellusElevate.Application.Features.Questionnaires.Commands
@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Core.Domains.Questionnaires.Common.Helpers
@using TrellusElevate.Core.Domains.Questionnaires.Entities
@implements IDisposable

@inject IDialogService DialogService
@inject QuestionnaireState QuestionnaireState
@inject ISnackbar Snackbar

<MudExpansionPanel Expanded="Item.Type == QuestionType.Group" Dense Class="mt-1" Style="border: 1px solid var(--mud-palette-lines-default);">
    <TitleContent>
        <div class="d-flex align-center">
            <MudText Color="Color.Primary" Typo="Typo.subtitle1">
                @Item.Prefix @Item.Text
            </MudText>
            <MudChip T="string" Size="Size.Small" Color="Color.Secondary" Class="ml-2">@Item.Type.ToString()</MudChip>
            <MudSpacer />
            <MudTooltip Text="Edit">
                <MudIconButton Size="Size.Small" Icon="@Icons.Material.Filled.Edit" 
                              Color="Color.Primary" 
                              OnClick="@(() => SaveQuestionnaireItemAsync(Item))" />
            </MudTooltip>
            <MudTooltip Text="Remove">
                <MudIconButton Size="Size.Small" Icon="@Icons.Material.Filled.Delete" 
                              Color="Color.Error" 
                              OnClick="@RemoveItem" />
            </MudTooltip>
        </div>
    </TitleContent>
    <ChildContent>
        <MudPaper Elevation="0" Class="pa-3">
            <MudGrid Spacing="1">
                <MudItem xs="12" sm="6">
                    <MudPaper Elevation="1" Class="d-flex gap-2 pa-2 rounded">
                        <MudText Typo="Typo.caption" Class="mt-1">Type:</MudText>
                        <MudChip T="string" Size="Size.Small" Color="Color.Primary">@Item.Type.ToString()</MudChip>
                        <MudText Typo="Typo.caption" Class="mt-1 ml-2">Prefix:</MudText>
                        <MudChip T="string" Size="Size.Small" Color="Color.Default">@Item.Prefix</MudChip>
                        <MudText Typo="Typo.caption" Class="mt-1 ml-2">LinkId:</MudText>
                        <MudChip T="string" Size="Size.Small" Color="Color.Default">@Item.LinkId</MudChip>
                    </MudPaper>
                </MudItem>

                @if (Item.Type.IsAnswerableType())
                {
                    <MudItem xs="12" sm="6">
                        <MudPaper Elevation="1" Class="d-flex gap-2 pa-2 rounded">
                            <MudIcon Size="Size.Small" Icon="@(Item.Required ? Icons.Material.Filled.Check : Icons.Material.Filled.Close)" 
                                    Color="@(Item.Required ? Color.Success : Color.Error)" Class="mt-1" />
                            <MudText Typo="Typo.caption" Class="mt-1">Required</MudText>
                            
                            <MudIcon Size="Size.Small" Icon="@(Item.Repeats ? Icons.Material.Filled.Check : Icons.Material.Filled.Close)" 
                                    Color="@(Item.Repeats ? Color.Success : Color.Error)" Class="mt-1 ml-2" />
                            <MudText Typo="Typo.caption" Class="mt-1">Repeats</MudText>
                            
                            @if (Item.Type is QuestionType.String or QuestionType.Text && Item.MaxLength.HasValue)
                            {
                                <MudText Typo="Typo.caption" Class="mt-1 ml-2">Max Length:</MudText>
                                <MudChip T="string" Size="Size.Small" Color="Color.Default">@Item.MaxLength</MudChip>
                            }
                        </MudPaper>
                    </MudItem>
                }

                @if (Item.EnableWhen.Any())
                {
                    <MudItem xs="12">
                        <MudPaper Elevation="1" Class="d-flex flex-wrap pa-2 rounded">
                            <MudText Typo="Typo.caption" Class="mt-1 mr-2">Enable when:</MudText>
                            @foreach (var rule in Item.EnableWhen)
                            {
                                <MudChip T="string" Size="Size.Small" Color="Color.Info" Class="ma-1">
                                    @rule.LinkId @rule.Operator @rule.Answer?.Value
                                </MudChip>
                            }
                            <MudText Typo="Typo.caption" Class="mt-1 ml-2">Behavior:</MudText>
                            <MudChip T="string" Size="Size.Small" Color="Color.Info">@Item.EnableBehavior.ToString()</MudChip>

                            @if (Item.DisabledDisplay.HasValue)
                            {
                                <MudText Typo="Typo.caption" Class="mt-1 ml-2">When disabled:</MudText>
                                <MudChip T="string" Size="Size.Small" Color="@(Item.DisabledDisplay == DisabledDisplay.Hidden ? Color.Error : Color.Warning)">
                                    @Item.DisabledDisplay.ToString()
                                </MudChip>
                            }
                        </MudPaper>
                    </MudItem>
                }

                @if (!Item.EnableWhen.Any() && Item.DisabledDisplay.HasValue)
                {
                    <MudItem xs="12">
                        <MudPaper Elevation="1" Class="d-flex flex-wrap pa-2 rounded">
                            <MudText Typo="Typo.caption" Class="mt-1 mr-2">When disabled:</MudText>
                            <MudChip T="string" Size="Size.Small" Color="@(Item.DisabledDisplay == DisabledDisplay.Hidden ? Color.Error : Color.Warning)">
                                @Item.DisabledDisplay.ToString()
                            </MudChip>
                        </MudPaper>
                    </MudItem>
                }
                
                @if (Item.Type.IsAnswerableType() && Item.Options.Any())
                {
                    <MudItem xs="12">
                        <MudPaper Elevation="1" Class="pa-2 rounded">
                            <MudText Typo="Typo.caption">Options:</MudText>
                            <div class="d-flex flex-wrap gap-1 mt-1">
                                @foreach (var option in Item.Options)
                                {
                                    <MudChip T="string" 
                                           Size="Size.Small" 
                                           Color="@(option.InitialSelected ? Color.Success : Color.Default)" 
                                           Variant="@(option.InitialSelected ? Variant.Filled : Variant.Outlined)">
                                        @(option.Answer?.GetValue<string>()) (@option.Title)
                                    </MudChip>
                                }
                            </div>
                        </MudPaper>
                    </MudItem>
                }

                @if (Item.Metadata?.Any() == true)
                {
                    <MudItem xs="12">
                        <MudPaper Elevation="1" Class="pa-2 rounded">
                            <MudText Typo="Typo.caption">Metadata:</MudText>
                            <MudStack Spacing="1" Class="mt-1">
                                @foreach (var metadata in Item.Metadata)
                                {
                                    <MudGrid Spacing="1">
                                        <MudItem xs="4">
                                            <MudText Typo="Typo.caption" Color="Color.Primary">@metadata.Key</MudText>
                                        </MudItem>
                                        <MudItem xs="8">
                                            <MudText Typo="Typo.body2">@metadata.Value?.ToString()</MudText>
                                        </MudItem>
                                    </MudGrid>
                                }
                            </MudStack>
                        </MudPaper>
                    </MudItem>
                }

                @if (Item.Type == QuestionType.Group)
                {
                    <MudItem xs="12">
                        <MudDivider Class="my-2" />
                        <MudText Typo="Typo.subtitle2">Group Items</MudText>
                        <MudPaper Elevation="1" Class="mt-2 pa-2 rounded">
                            @if (Item.Items.Count == 0)
                            {
                                <MudAlert Severity="Severity.Info" Dense>No items in this group</MudAlert>
                            }
                            else
                            {
                                <MudDropContainer @ref="_dropContainer" T="QuestionnaireItemDto" Items="Item.Items.OrderBy(x => x.OrderNumber).ToList()"
                                                ItemsSelector="@((item, dropzone) => dropzone == $"group-{Item.Id}" && item.ParentQuestionnaireItemId == Item.Id)"
                                                ItemDropped="@(async (dropItem) => await ItemDropped(dropItem))">
                                    <ChildContent>
                                        <MudDropZone T="QuestionnaireItemDto" Identifier="@($"group-{Item.Id}")" AllowReorder="true" />
                                    </ChildContent>

                                    <ItemRenderer>
                                        <QuestionnaireItem
                                            Item="@context" />
                                    </ItemRenderer>
                                </MudDropContainer>
                            }
                            
                            <MudButton StartIcon="@Icons.Material.Filled.Add"
                                    Variant="Variant.Filled"
                                    Size="Size.Small"
                                    Color="Color.Primary"
                                    Class="mt-2"
                                    OnClick="@(_ => SaveQuestionnaireItemAsync(null, Item))">
                                New question
                            </MudButton>
                        </MudPaper>
                    </MudItem>
                }
            </MudGrid>
        </MudPaper>
    </ChildContent>
</MudExpansionPanel>


@code {
    [Parameter] public QuestionnaireItemDto Item { get; set; } = null!;

    private MudDropContainer<QuestionnaireItemDto> _dropContainer = null!;

    protected override void OnInitialized()
    {
        QuestionnaireState.OnChange += StateHasChanged;
        QuestionnaireState.OnChange += () => _dropContainer?.Refresh();
    }

    public void Dispose()
    {
        QuestionnaireState.OnChange -= StateHasChanged;
        QuestionnaireState.OnChange -= () => _dropContainer?.Refresh();
    }
    
    private async Task SaveQuestionnaireItemAsync(QuestionnaireItemDto? data = null, QuestionnaireItemDto? parentItemDto = null)
    {
        var isEdit = data != null;
        var options = new DialogOptions { MaxWidth = MaxWidth.Medium, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveQuestionnaireItemDialog>
        {
            { x => x.Data, data },
            { x => x.Questionnaire, QuestionnaireState.Questionnaire },
            { x => x.ParentData, parentItemDto }
        };

        var dialog = await DialogService.ShowAsync<SaveQuestionnaireItemDialog>($"{(isEdit ? "Update" : "Create")} questionnaire item", parameters, options);
        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            // The state will handle the updates automatically
            // No need for manual collection manipulation or callbacks
        }
    }

    private async Task RemoveItem()
    {
        var parameters = new DialogParameters<MudMessageBox>
        {
            { x => x.Title, "Delete Question" },
            { x => x.Message, $"Are you sure you want to delete the question \"{Item.Text}\"?" },
            { x => x.YesText, "Delete" },
            { x => x.NoText, "Cancel" }
        };

        var options = new DialogOptions
        {
            MaxWidth = MaxWidth.Small,
            CloseButton = true,
            BackdropClick = false
        };

        var dialog = await DialogService.ShowAsync<MudMessageBox>("Delete Question", parameters, options);
        var result = await dialog.Result;

        if (result.Canceled) return;

        var success = await QuestionnaireState.RemoveQuestionnaireItemAsync(Item.Id);

        if (success)
        {
            Snackbar.Add($"Question \"{Item.Text}\" has been deleted", Severity.Success);
        }
        // Error handling is done in the state
    }

    private async Task ItemDropped(MudItemDropInfo<QuestionnaireItemDto> dropItem)
    {
        if (dropItem.Item == null) return;

        // Store original order for comparison
        var originalOrder = Item.Items.Select((item, index) => new { item.Id, OrderNumber = index + 1 }).ToList();

        // Remove and insert the item
        Item.Items.Remove(dropItem.Item);
        Item.Items.Insert(dropItem.IndexInZone, dropItem.Item);

        // Update order numbers in the DTO
        for (var i = 0; i < Item.Items.Count; i++)
        {
            Item.Items[i].OrderNumber = i + 1;
        }

        // Check if order actually changed
        var newOrder = Item.Items.Select((item, index) => new { item.Id, OrderNumber = index + 1 }).ToList();
        var orderChanged = !originalOrder.SequenceEqual(newOrder);

        if (orderChanged)
        {
            // Create order map for the command - only include the nested items that changed
            var orderMap = Item.Items.ToDictionary(item => item.Id, item => item.OrderNumber);

            // Use state to handle reordering
            var success = await QuestionnaireState.ReorderQuestionnaireItemsAsync(orderMap);

            if (success)
            {
                Snackbar.Add("Questions reordered successfully!", Severity.Success);
            }
            else
            {
                // Refresh the drop container to restore original order
                _dropContainer.Refresh();
            }
        }
    }
}