@page "/administration/questionnaires"
@using TrellusElevate.Application.Features.Questionnaires.Enums
@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Application.Features.Questionnaires.Queries
@using TrellusElevate.Core.Domains.Questionnaires.Entities
@using TrellusElevate.Web.Common.Extensions

@inject IMediator Mediator
@inject NavigationManager Nav
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>Questionnaires</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Administration", href: "/administration", icon: Icons.Material.Filled.Settings),
                   new BreadcrumbItem("Questionnaires", href: "/administration/questionnaires", icon: Icons.Material.Filled.QuestionMark),
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="CreateAsync" Variant="Variant.Filled"
               Color="Color.Primary">
        New
    </MudButton>
</MudToolBar>

<MudDataGrid @ref="_dataGrid" T="QuestionnaireListItemDto" ServerData="ServerReload" Filterable="false" Striped="true"
             Hover="true"
             SortMode="SortMode.Single"
             RowClick="@(row => Nav.NavigateTo($"/administration/questionnaires/{row.Item.Id}"))">
    <ToolBarContent>
        <MudStack Spacing="3" Row="true" Wrap="Wrap.Wrap">
            <MudTextField Class="pt-1"  
                          @bind-Value="_query.SearchTerm" T="string"
                          @bind-Value:after="Filter" Placeholder="Search"
                          Clearable="true"
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"/>
            
            <MudSelect   Dense="true" T="QuestionnaireStatus"
                       MultiSelectionTextFunc="@(val => string.Join(", ", val!.Select(s => Enum.Parse<QuestionnaireStatus>(s!).ToString())))"
                       Label="Status" MultiSelection="true" Clearable="true"
                       @bind-SelectedValues="_query.Status" @bind-SelectedValues:after="Filter">
                @foreach (var item in Enum.GetValues<QuestionnaireStatus>())
                {
                    <MudSelectItem T="QuestionnaireStatus" Value="item">@item.ToString()</MudSelectItem>
                }
            </MudSelect>

        </MudStack>
    </ToolBarContent>

    <Columns>
        <PropertyColumn Property="x => x.Title" Title="Title"/>
        <PropertyColumn Property="x => x.Name" Title="Name (System name)"/>
        <PropertyColumn Property="x => x.Status" Title="Status">
            <CellTemplate>
                <MudChip T="string" Color="@context.Item.Status.GetColor()">
                    @context.Item.Status.ToString()
                </MudChip>
            </CellTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.CreatedAt" Title="Created at" Format="d"/>
        <PropertyColumn Property="x => x.UpdatedAt" Title="Updated at" Format="d"/>
    </Columns>
    <PagerContent>
        <MudDataGridPager T="QuestionnaireListItemDto"/>
    </PagerContent>
    <NoRecordsContent>
        No records found
    </NoRecordsContent>
</MudDataGrid>

@code {
    
    private MudDataGrid<QuestionnaireListItemDto> _dataGrid = null!;
    readonly GetQuestionnaireListQuery _query = new();

    private async Task<GridData<QuestionnaireListItemDto>> ServerReload(GridState<QuestionnaireListItemDto> state)
    {
        _query.Page = state.Page + 1;
        _query.PageSize = state.PageSize;

        var sortDefinition = state.SortDefinitions.FirstOrDefault();
        if (sortDefinition != null)
        {
            _query.SortBy = Enum.Parse<QuestionnaireSort>(sortDefinition.SortBy);
            _query.SortDirection = sortDefinition.Descending ? Application.Common.Enums.SortDirection.Desc : Application.Common.Enums.SortDirection.Asc;
        }

        var result = await Mediator.Send(_query);

        return result.Match(
            value => new GridData<QuestionnaireListItemDto>
            {
                TotalItems = value.TotalCount,
                Items = value.Items
            },
            error =>
            {
                Snackbar.Add($"Error loading questionnaires: {error.Code}: {error.Description}", Severity.Error);
                return new GridData<QuestionnaireListItemDto>();
            });
    }

    private Task Filter()
    {
        return _dataGrid.ReloadServerData();
    }
    
    private async Task CreateAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveQuestionnaireDialog> { { x => x.Data, null } };

        var dialog = await DialogService.ShowAsync<SaveQuestionnaireDialog>("Create questionnaire", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            Nav.NavigateTo($"/administration/questionnaires/{(result.Data as QuestionnaireDto)?.Id}");
        }
    }
}