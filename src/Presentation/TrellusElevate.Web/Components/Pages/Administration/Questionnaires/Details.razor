@page "/administration/questionnaires/{QuestionnaireId:guid}"
@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Application.Features.Questionnaires.Queries
@using TrellusElevate.Application.Features.Questionnaires.Commands
@using TrellusElevate.Web.Common.Extensions
@implements IDisposable

@inject QuestionnaireState QuestionnaireState
@inject ISnackbar Snackbar
@inject IDialogService DialogService

@if (QuestionnaireState.Error != null)
{
    <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
              CloseIconClicked="QuestionnaireState.ClearError">
        @QuestionnaireState.Error?.Code: @QuestionnaireState.Error?.Description
    </MudAlert>
}

@if (QuestionnaireState.Questionnaire == null!)
{
    <MudProgressLinear Indeterminate="true"/>
    return;
}

<PageTitle>Assessments | Details</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Administration", href: "/administration", icon: Icons.Material.Filled.Settings),
                   new BreadcrumbItem("Questionnaires", href: "/administration/questionnaires", icon: Icons.Material.Filled.QuestionMark),
                   new BreadcrumbItem(QuestionnaireState.Questionnaire.Title, href: $"/administration/questionnaires/{QuestionnaireId}", icon: Icons.Material.Filled.QuestionMark),
               ])">
    </MudBreadcrumbs>
</MudToolBar>

<MudGrid>
    <MudItem md="3">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Questionnaire info</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack>
                    <div>
                        <MudText Typo="Typo.body2">Title</MudText>
                        <MudText Typo="Typo.body1">@QuestionnaireState.Questionnaire.Title</MudText>
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">System Name</MudText>
                        <MudText Typo="Typo.body1">@QuestionnaireState.Questionnaire.Name</MudText>
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">Description</MudText>
                        <MudText Typo="Typo.body1">@QuestionnaireState.Questionnaire.Description</MudText>
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">Status</MudText>
                        <MudChip T="string" Color="@QuestionnaireState.Questionnaire.Status.GetColor()">
                            @QuestionnaireState.Questionnaire.Status.ToString()
                        </MudChip>
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">Created at</MudText>
                        <MudText Typo="Typo.body1">@QuestionnaireState.Questionnaire.CreatedAt.ToString("g")</MudText>
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">Updated at</MudText>
                        <MudText Typo="Typo.body1">@QuestionnaireState.Questionnaire.UpdatedAt?.ToString("g")</MudText>
                    </div>

                    @if (QuestionnaireState.Questionnaire.Metadata?.Any() == true)
                    {
                        <div>
                            <MudText Typo="Typo.body2">Metadata</MudText>
                            <MudStack Spacing="1" Class="mt-2">
                                @foreach (var metadata in QuestionnaireState.Questionnaire.Metadata)
                                {
                                    <MudPaper Elevation="1" Class="pa-2">
                                        <MudGrid Spacing="1">
                                            <MudItem xs="4">
                                                <MudText Typo="Typo.caption" Color="Color.Primary">@metadata.Key</MudText>
                                            </MudItem>
                                            <MudItem xs="8">
                                                <MudText Typo="Typo.body2">@metadata.Value?.ToString()</MudText>
                                            </MudItem>
                                        </MudGrid>
                                    </MudPaper>
                                }
                            </MudStack>
                        </div>
                    }
                </MudStack>
            </MudCardContent>
            <MudCardActions>
                <MudButton StartIcon="@Icons.Material.Filled.Edit"
                           OnClick="@SaveAsync"
                           Variant="Variant.Filled"
                           Color="Color.Primary">
                    Edit
                </MudButton>
            </MudCardActions>
        </MudCard>
    </MudItem>
    <MudItem md="9">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Questions</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack Spacing="2">

                    <MudDropContainer @ref="_dropContainer" T="QuestionnaireItemDto"
                                      Items="QuestionnaireState.Questionnaire.Items.OrderBy(x => x.OrderNumber).ToList()"
                                      ItemsSelector="@((item, dropzone) => dropzone == "main-questions" && item.ParentQuestionnaireItemId == null)"
                                      ItemDropped="@(async (dropItem) => await ItemDropped(dropItem))">
                        <ChildContent>
                            <MudStack>
                                <MudDropZone T="QuestionnaireItemDto" Identifier="main-questions" AllowReorder="true"/>
                            </MudStack>
                        </ChildContent>

                        <ItemRenderer>
                            <QuestionnaireItem
                                Item="@context"/>
                        </ItemRenderer>

                    </MudDropContainer>

                    <MudButton StartIcon="@Icons.Material.Filled.Add"
                               Variant="Variant.Filled"
                               Color="Color.Primary"
                               OnClick="@(() => SaveQuestionnaireItemAsync())">
                        New question
                    </MudButton>
                </MudStack>
            </MudCardContent>
        </MudCard>
    </MudItem>
</MudGrid>


@code {
    [Parameter] public Guid QuestionnaireId { get; set; }

    private MudDropContainer<QuestionnaireItemDto> _dropContainer = null!;

    protected override async Task OnInitializedAsync()
    {
        QuestionnaireState.OnChange += StateHasChanged;
        QuestionnaireState.OnChange += () => _dropContainer?.Refresh();
        await QuestionnaireState.LoadQuestionnaireAsync(QuestionnaireId);
    }

    public void Dispose()
    {
        QuestionnaireState.OnChange -= StateHasChanged;
        QuestionnaireState.OnChange -= () => _dropContainer?.Refresh();
    }


    private async Task SaveAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveQuestionnaireDialog> { { x => x.Data, QuestionnaireState.Questionnaire } };

        var dialog = await DialogService.ShowAsync<SaveQuestionnaireDialog>("Update questionnaire", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            QuestionnaireState.SetQuestionnaire((result.Data as QuestionnaireDto)!);
        }
    }

    private async Task SaveQuestionnaireItemAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveQuestionnaireItemDialog>
        {
            { x => x.Questionnaire, QuestionnaireState.Questionnaire }
        };

        var dialog = await DialogService.ShowAsync<SaveQuestionnaireItemDialog>("Create questionnaire item", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            // The state will handle adding the item and notifying changes
            _dropContainer.Refresh();
        }
    }

    private async Task ItemDropped(MudItemDropInfo<QuestionnaireItemDto> dropItem)
    {
        if (dropItem.Item == null || QuestionnaireState.Questionnaire == null) return;

        // Store original order for comparison
        var originalOrder = QuestionnaireState.Questionnaire.Items.Select((item, index) => new { item.Id, OrderNumber = index + 1 }).ToList();

        // Remove and insert the item
        QuestionnaireState.Questionnaire.Items.Remove(dropItem.Item);
        QuestionnaireState.Questionnaire.Items.Insert(dropItem.IndexInZone, dropItem.Item);

        // Update order numbers in the DTO
        for (var i = 0; i < QuestionnaireState.Questionnaire.Items.Count; i++)
        {
            QuestionnaireState.Questionnaire.Items[i].OrderNumber = i + 1;
        }

        // Check if order actually changed
        var newOrder = QuestionnaireState.Questionnaire.Items.Select((item, index) => new { item.Id, OrderNumber = index + 1 }).ToList();
        var orderChanged = !originalOrder.SequenceEqual(newOrder);

        if (orderChanged)
        {
            // Create order map for the command
            var orderMap = QuestionnaireState.Questionnaire.Items.ToDictionary(item => item.Id, item => item.OrderNumber);

            // Use state to handle reordering
            var success = await QuestionnaireState.ReorderQuestionnaireItemsAsync(orderMap);

            if (success)
            {
                Snackbar.Add("Questions reordered successfully!", Severity.Success);
            }
            // Error handling is done in the state
        }
    }

}