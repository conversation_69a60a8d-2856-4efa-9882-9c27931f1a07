using TrellusElevate.Application.Features.Questionnaires.Commands;
using TrellusElevate.Application.Features.Questionnaires.Models;
using TrellusElevate.Application.Features.Questionnaires.Queries;
using TrellusElevate.Web.Common.State;
using TrellusElevate.Presentation.Features.Questionnaires.Models;
using TrellusElevate.Presentation.Features.Questionnaires.Mappers;

namespace TrellusElevate.Web.Components.Pages.Administration.Questionnaires;

public sealed class QuestionnaireState(IMediator mediator) : BaseState
{
    public Guid? QuestionnaireId { get; private set; }
    public QuestionnaireDto? Questionnaire { get; private set; }

    public async Task<QuestionnaireDto> LoadQuestionnaireAsync(Guid questionnaireId)
    {
        if (QuestionnaireId == questionnaireId && Questionnaire != null) return Questionnaire;
        
        QuestionnaireId = questionnaireId;
        var result = await mediator.Send(new GetQuestionnaireByIdQuery(questionnaireId));

        result.Switch(
            value => Questionnaire = value,
            SetError
        );

        NotifyStateChanged();
        return Questionnaire!;
    }

    public async Task<QuestionnaireItemDto?> CreateQuestionnaireItemAsync(SaveQuestionnaireItemRequest request)
    {
        if (Questionnaire == null) return null;

        var command = request.ToCommand(Questionnaire.Id);
        var result = await mediator.Send(command);

        return result.Match(
            value =>
            {
                // Add to the appropriate collection at the correct position
                if (request.ParentQuestionnaireItemId.HasValue)
                {
                    var parentItem = FindQuestionnaireItem(request.ParentQuestionnaireItemId.Value);
                    if (parentItem != null)
                    {
                        // Insert at the correct position based on order number
                        var insertIndex = parentItem.Items.Count(x => x.OrderNumber < value.OrderNumber);
                        parentItem.Items.Insert(insertIndex, value);
                    }
                }
                else
                {
                    // Insert at the correct position based on order number
                    var insertIndex = Questionnaire!.Items.Count(x => x.OrderNumber < value.OrderNumber);
                    Questionnaire.Items.Insert(insertIndex, value);
                }

                NotifyStateChanged();
                return value;
            },
            error =>
            {
                SetError(error);
                return null!;
            }
        );
    }

    public async Task<QuestionnaireItemDto?> UpdateQuestionnaireItemAsync(Guid questionnaireItemId, SaveQuestionnaireItemRequest request)
    {
        if (Questionnaire == null) return null;

        var command = request.ToCommand(Questionnaire.Id, questionnaireItemId);
        var result = await mediator.Send(command);

        return result.Match(
            value =>
            {
                // Update the item in the collection
                var existingItem = FindQuestionnaireItem(questionnaireItemId);
                if (existingItem != null)
                {
                    UpdateQuestionnaireItemInCollection(existingItem, value);
                }

                NotifyStateChanged();
                return value;
            },
            error =>
            {
                SetError(error);
                return null!;
            }
        );
    }

    public async Task<bool> RemoveQuestionnaireItemAsync(Guid questionnaireItemId)
    {
        if (Questionnaire == null) return false;

        var command = new RemoveQuestionnaireItemCommand(Questionnaire.Id, questionnaireItemId);
        var result = await mediator.Send(command);

        return result.Match(
            () =>
            {
                // Remove from collection
                RemoveQuestionnaireItemFromCollection(questionnaireItemId);
                NotifyStateChanged();
                return true;
            },
            error =>
            {
                SetError(error);
                return false;
            }
        );
    }

    public async Task<bool> ReorderQuestionnaireItemsAsync(Dictionary<Guid, int> orderMap)
    {
        if (Questionnaire == null) return false;

        var command = new ReorderQuestionnaireItemsCommand(Questionnaire.Id, orderMap);
        var result = await mediator.Send(command);

        return result.Match(
            () =>
            {
                // Update order numbers in local collection
                foreach (var (itemId, newOrder) in orderMap)
                {
                    var item = FindQuestionnaireItem(itemId);
                    if (item != null)
                    {
                        item.OrderNumber = newOrder;
                    }
                }
                
                NotifyStateChanged();
                return true;
            },
            error =>
            {
                SetError(error);
                return false;
            }
        );
    }

    public void SetQuestionnaire(QuestionnaireDto questionnaire)
    {
        if (QuestionnaireId != questionnaire.Id) return;
        Questionnaire = questionnaire;
        NotifyStateChanged();
    }

    public async Task<bool> RefreshQuestionnaireAsync()
    {
        if (QuestionnaireId == null) return false;

        var result = await mediator.Send(new GetQuestionnaireByIdQuery(QuestionnaireId.Value));

        return result.Match(
            value =>
            {
                Questionnaire = value;
                NotifyStateChanged();
                return true;
            },
            error =>
            {
                SetError(error);
                return false;
            }
        );
    }

    private QuestionnaireItemDto? FindQuestionnaireItem(Guid itemId)
    {
        if (Questionnaire == null) return null;
        
        return FindQuestionnaireItemRecursive(Questionnaire.Items, itemId);
    }

    private static QuestionnaireItemDto? FindQuestionnaireItemRecursive(IEnumerable<QuestionnaireItemDto> items, Guid itemId)
    {
        foreach (var item in items)
        {
            if (item.Id == itemId) return item;
            
            var found = FindQuestionnaireItemRecursive(item.Items, itemId);
            if (found != null) return found;
        }
        
        return null;
    }

    private void UpdateQuestionnaireItemInCollection(QuestionnaireItemDto existingItem, QuestionnaireItemDto updatedItem)
    {
        existingItem.LinkId = updatedItem.LinkId;
        existingItem.Text = updatedItem.Text;
        existingItem.Type = updatedItem.Type;
        existingItem.OrderNumber = updatedItem.OrderNumber;
        existingItem.Required = updatedItem.Required;
        existingItem.EnableWhen = updatedItem.EnableWhen;
        existingItem.EnableBehavior = updatedItem.EnableBehavior;
        existingItem.DisabledDisplay = updatedItem.DisabledDisplay;
        existingItem.Prefix = updatedItem.Prefix;
        existingItem.MaxLength = updatedItem.MaxLength;
        existingItem.ReadOnly = updatedItem.ReadOnly;
        existingItem.Repeats = updatedItem.Repeats;
        existingItem.Options = updatedItem.Options;
        existingItem.Initial = updatedItem.Initial;
        existingItem.Metadata = updatedItem.Metadata;
    }

    private void RemoveQuestionnaireItemFromCollection(Guid itemId)
    {
        if (Questionnaire == null) return;
        
        RemoveQuestionnaireItemRecursive(Questionnaire.Items, itemId);
    }

    private static bool RemoveQuestionnaireItemRecursive(IList<QuestionnaireItemDto> items, Guid itemId)
    {
        for (int i = 0; i < items.Count; i++)
        {
            if (items[i].Id == itemId)
            {
                items.RemoveAt(i);
                return true;
            }
            
            if (RemoveQuestionnaireItemRecursive(items[i].Items, itemId))
            {
                return true;
            }
        }
        
        return false;
    }
}
