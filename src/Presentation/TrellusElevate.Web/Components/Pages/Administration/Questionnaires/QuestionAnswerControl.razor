@using TrellusElevate.Core.Domains.Questionnaires.Entities
@using TrellusElevate.Core.Domains.Questionnaires.ValueObjects

@switch (Type)
{
    case AnswerType.Boolean:
        <MudSelect T="bool?"
                   @bind-Value="BooleanValue"
                   Clearable="true"
                   Variant="@Variant"
                   Margin="@Margin"
                   Disabled="@Disabled"
                   Required="@Required"
                   Label="@Label">
            <MudSelectItem T="bool?" Value="true">True</MudSelectItem>
            <MudSelectItem T="bool?" Value="false">False</MudSelectItem>
        </MudSelect>
        break;

    case AnswerType.Decimal:
        <MudNumericField @bind-Value="DecimalValue"
                         Label="@Label"
                         Variant="@Variant"
                         Margin="@Margin"
                         Disabled="@Disabled"
                         Clearable="true"
                         Required="@Required"/>
        break;

    case AnswerType.Integer:
        <MudNumericField @bind-Value="IntegerValue"
                         Label="@Label"
                         Variant="@Variant"
                         Margin="@Margin"
                         Disabled="@Disabled"
                         Clearable="true"
                         Required="@Required"/>
        break;

    case AnswerType.Date:
        <MudDateWheelPicker Value="@DateValue"
                            ValueChanged="@((DateTime? value) => DateValue = value)"
                            DateView="DateView.Date"
                            Label="@Label"
                            Variant="@Variant"
                            Margin="@Margin"
                            Disabled="@Disabled"
                            Clearable="true"
                            Required="@Required"/>
        break;

    case AnswerType.DateTime:
        <MudDateWheelPicker Value="@DateTimeValue"
                            ValueChanged="@((DateTime? value) => DateTimeValue = value)"
                            DateView="DateView.Both"
                            DateFormat="MM/dd/yyyy hh:mm"
                            Label="@Label"
                            Variant="@Variant"
                            Margin="@Margin"
                            Disabled="@Disabled"
                            Clearable="true"
                            Required="@Required"/>
        break;

    case AnswerType.Time:
        <MudDateWheelPicker Value="@TimeOnlyValue"
                            ValueChanged="@((DateTime? value) => TimeOnlyValue = value)"
                            DateView="DateView.Time"
                            DateFormat="hh:mm"
                            Label="@Label"
                            Variant="@Variant"
                            Margin="@Margin"
                            Disabled="@Disabled"
                            Required="@Required"/>
        break;

    case AnswerType.String:
        <MudTextField @bind-Value="StringValue"
                      Label="@Label"
                      Variant="@Variant"
                      Margin="@Margin"
                      Disabled="@Disabled"
                      Clearable="true"
                      Required="@Required"/>
        break;

    case AnswerType.Text:
        <MudTextField @bind-Value="TextValue"
                      Label="@Label"
                      Variant="@Variant"
                      Margin="@Margin"
                      Lines="3"
                      Disabled="@Disabled"
                      Clearable="true"
                      Required="@Required"/>
        break;

    case AnswerType.Url:
        <MudTextField @bind-Value="UrlValue"
                      Label="@Label"
                      Variant="@Variant"
                      Margin="@Margin"
                      Adornment="Adornment.Start"
                      AdornmentIcon="@Icons.Material.Filled.Link"
                      Disabled="@Disabled"
                      Clearable="true"
                      Required="@Required"/>
        break;


    default:
        <MudAlert Severity="Severity.Warning">Unsupported question type: @Value?.Type</MudAlert>
        break;
}

@code {
    [Parameter] public AnswerType? Type { get; set; }
    [Parameter] public QuestionAnswer? Value { get; set; }

    [Parameter] public EventCallback<QuestionAnswer?> ValueChanged { get; set; }

    [Parameter] public string Label { get; set; } = "Answer";

    [Parameter] public bool Required { get; set; }

    [Parameter] public bool Disabled { get; set; }

    [Parameter] public Variant Variant { get; set; } = Variant.Outlined;

    [Parameter] public Margin Margin { get; set; } = Margin.Dense;

    protected override void OnParametersSet()
    {
        base.OnParametersSet();

        // Initialize component based on the current value
        if (Value != null)
        {
            Type = Value.Type ?? Type;

            switch (Type)
            {
                case AnswerType.Boolean:
                    BooleanValue = Value.GetValue<bool?>();
                    break;
                case AnswerType.Decimal:
                    DecimalValue = Value.GetValue<decimal?>();
                    break;
                case AnswerType.Integer:
                    IntegerValue = Value.GetValue<int?>();
                    break;
                case AnswerType.Date:
                    var dateOnly = Value.GetValue<DateOnly?>();
                    DateValue = dateOnly?.ToDateTime(TimeOnly.MinValue);
                    break;
                case AnswerType.DateTime:
                    DateTimeValue = Value.GetValue<DateTime?>();
                    break;
                case AnswerType.Time:
                    var timeOnly = Value.GetValue<TimeOnly?>();
                    TimeOnlyValue = timeOnly.HasValue ? DateTime.Today.Add(timeOnly.Value.ToTimeSpan()) : null;
                    break;
                case AnswerType.String:
                case AnswerType.Text:
                case AnswerType.Url:
                    StringValue = Value.GetValue<string>();
                    break;
            }
        }
    }

    // Property getters and setters for different question types

    // Boolean
    private bool? BooleanValue
    {
        get => Value?.GetValue<bool?>();
        set => _ = UpdateValue(value);
    }

    // Decimal
    private decimal? DecimalValue
    {
        get => Value?.GetValue<decimal?>();
        set => _ = UpdateValue(value);
    }

    // Integer
    private int? IntegerValue
    {
        get => Value?.GetValue<int?>();
        set => _ = UpdateValue(value);
    }

    // Date and DateTime
    private DateTime? DateTimeValue
    {
        get => Value?.GetValue<DateTime?>();
        set => _ = UpdateValue(value);
    }

    // Date
    private DateTime? DateValue
    {
        get => Value?.GetValue<DateOnly?>()?.ToDateTime(TimeOnly.MinValue);
        set => _ = UpdateValue<DateOnly?>(value.HasValue ? DateOnly.FromDateTime(value.Value) : null);
    }

    // Time - using DateTime for MudDateWheelPicker with DateView.Time
    private DateTime? TimeOnlyValue
    {
        get => Value?.GetValue<TimeOnly?>() is TimeOnly to ? DateTime.Today.Add(to.ToTimeSpan()) : null;
        set => _ = UpdateValue<TimeOnly?>(value.HasValue ? TimeOnly.FromDateTime(value.Value) : null);
    }

    // String, Text, Url
    private string? StringValue
    {
        get => Value?.GetValue<string>();
        set => _ = UpdateValue(value);
    }

    // Text (same as String but with multiline support)
    private string? TextValue
    {
        get => StringValue;
        set => _ = UpdateValue(value);
    }

    // Url (same as String but with URL validation)
    private string? UrlValue
    {
        get => StringValue;
        set => _ = UpdateValue(value);
    }

    // Helper method to update the value and raise the ValueChanged event
    private async Task UpdateValue<T>(T? value)
    {
        if (value == null)
        {
            await ValueChanged.InvokeAsync(null);
            return;
        }

        var newValue = new QuestionAnswer(value, Type);

        if (!Equals(Value, newValue))
        {
            await ValueChanged.InvokeAsync(newValue);
        }
    }

}