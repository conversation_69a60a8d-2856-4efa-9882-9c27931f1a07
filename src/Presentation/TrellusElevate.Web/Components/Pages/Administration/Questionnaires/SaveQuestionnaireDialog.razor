@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Core.Domains.Questionnaires.Entities
@using TrellusElevate.Presentation.Features.Questionnaires.Mappers
@using TrellusElevate.Presentation.Features.Questionnaires.Models
@using Error = TrellusElevate.Core.Common.Errors.Error
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog!.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudTextField  
                              @bind-Value="_request.Title"
                              For="@(() => _request.Title)"
                              Immediate="true"
                              Label="Title"/>

                <MudTextField  
                              @bind-Value="_request.Name"
                              For="@(() => _request.Name)"
                              Immediate="true"
                              Label="Name"
                              HelperText="System unique name"/>

                <MudTextField  
                              @bind-Value="_request.Description"
                              Lines="3"
                              For="@(() => _request.Description)"
                              Immediate="true"
                              Label="Description"/>

                @if (_isEdit)
                {

                    <MudSelect Dense="true"
                               T="QuestionnaireStatus"
                               Label="Status"
                               @bind-Value="_request.Status">
                        @foreach (var enumItem in Enum.GetValues<QuestionnaireStatus>())
                        {
                            <MudSelectItem T="QuestionnaireStatus" Value="enumItem">
                                @enumItem.ToString()
                            </MudSelectItem>
                        }
                    </MudSelect>
                }

            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Cancel</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Save
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public QuestionnaireDto? Data { get; set; }

    private MudForm? _form;
    private bool _isEdit;
    private bool _loading;
    private Error? _error;

    private readonly SaveQuestionnaireRequest _request = new();
    private readonly SaveQuestionnaireRequestValidator _validator = new();

    protected override void OnInitialized()
    {
        _isEdit = Data is not null;
        if (_isEdit)
        {
            _request.Name = Data!.Name;
            _request.Description = Data.Description;
            _request.Status = Data.Status;
            _request.Title = Data.Title;
        }
    }
    
    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            _isEdit ? await Mediator.Send(_request.ToCommand(Data!.Id)) : await Mediator.Send(_request.ToCommand());

        result.Switch(value =>
        {
            Snackbar.Add($"Questionnaire {result.Value.Name} is {(_isEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();
}