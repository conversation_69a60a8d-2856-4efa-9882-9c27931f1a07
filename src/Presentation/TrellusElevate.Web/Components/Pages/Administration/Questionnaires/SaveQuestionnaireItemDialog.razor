@using TrellusElevate.Application.Common.Extensions
@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Core.Domains.Questionnaires.Common.Helpers
@using TrellusElevate.Core.Domains.Questionnaires.Entities
@using TrellusElevate.Core.Domains.Questionnaires.Mappers
@using TrellusElevate.Core.Domains.Questionnaires.ValueObjects
@using TrellusElevate.Presentation.Features.Questionnaires.Models
@using TrellusElevate.Web.Components.Shared
@using Error = TrellusElevate.Core.Common.Errors.Error
@inject ISnackbar Snackbar
@inject QuestionnaireState QuestionnaireState

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog!.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form" Validation="@_validator.AsMudFormValidation()" ValidationDelay="0">
            <MudStack Spacing="3">
                <!-- Basic Information Section -->
                <MudGrid>
                    <MudItem xs="12" sm="6">
                        <MudSelect
                            T="QuestionType"
                            Label="Type"
                            @bind-Value="_request.Type"
                            For="@(() => _request.Type)"
                            HelperText="Type of the question">
                            @foreach (var enumItem in Enum.GetValues<QuestionType>())
                            {
                                <MudSelectItem T="QuestionType" Value="enumItem">
                                    @enumItem.ToString()
                                </MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                    <MudItem xs="12" sm="3">
                        <MudTextField @bind-Value="_request.Prefix"
                                      For="@(() => _request.Prefix)"
                                      Immediate="true"
                                      Label="Prefix"
                                      HelperText="E.g. 1(a), 2.5.3"/>
                    </MudItem>
                    <MudItem xs="12" sm="3">
                        <MudTextField @bind-Value="_request.LinkId"
                                      For="@(() => _request.LinkId)"
                                      Immediate="true"
                                      Label="Link Id"
                                      HelperText="System unique id"/>
                    </MudItem>
                </MudGrid>

                <MudTextField @bind-Value="_request.Text"
                              Lines="2"
                              For="@(() => _request.Text)"
                              Immediate="true"
                              Label="Text"
                              HelperText="Question text"/>

                @if (_request.Type.IsAnswerableType())
                {
                    <!-- Answer Configuration Section -->
                    <MudGrid>
                        <MudItem xs="12" sm="6">
                            <MudStack Row Spacing="2">
                                <MudCheckBox @bind-Value="_request.Required">Required</MudCheckBox>
                                @if (_request.Options.Count > 1)
                                {
                                    <MudCheckBox @bind-Value="_request.Repeats">Repeats (for multi-select)</MudCheckBox>
                                }
                            </MudStack>
                        </MudItem>
                        @if (_request.Options.Any() || (_request.Type is QuestionType.Integer or QuestionType.Decimal && _request.Options.Any()))
                        {
                            <MudItem xs="12" sm="3">
                                <MudSelect T="UiElementType"
                                           Label="UI Element Type"
                                           @bind-Value="_request.UiElementType"
                                           HelperText="How to display this question">
                                    @foreach (var enumItem in Enum.GetValues<UiElementType>())
                                    {
                                        @if (IsUiElementTypeValid(enumItem))
                                        {
                                            <MudSelectItem T="UiElementType" Value="enumItem">
                                                @enumItem.ToString()
                                            </MudSelectItem>
                                        }
                                    }
                                </MudSelect>
                            </MudItem>
                        }
                        <MudItem xs="12" sm="3">
                            <MudSelect T="DisabledDisplay ?"
                                       Label="Disabled display"
                                       @bind-Value="_request.DisabledDisplay"
                                       Clearable="true">
                                @foreach (var enumItem in Enum.GetValues<DisabledDisplay>())
                                {
                                    <MudSelectItem T="DisabledDisplay ?" Value="enumItem">
                                        @enumItem.ToString()
                                    </MudSelectItem>
                                }
                            </MudSelect>
                        </MudItem>
                        @if (_request.Type is QuestionType.String or QuestionType.Text)
                        {
                            <MudItem xs="12" sm="3">
                                <MudNumericField @bind-Value="_request.MaxLength"
                                                 Label="Max length"
                                                 HelperText="Character limit"/>
                            </MudItem>
                        }
                    </MudGrid>
                }

                <!-- Conditional Logic Section -->
                <MudExpansionPanel Text="@($"Conditional Logic: {(_request.EnableBehavior?.ToString() ?? "None")}")">
                    <MudStack Spacing="3">
                        <div>
                            <MudText Typo="Typo.subtitle2" Class="mb-2">Enable Behavior</MudText>
                            <MudRadioGroup T="EnableWhenBehavior ?" @bind-Value="_request.EnableBehavior" Class="d-flex flex-row gap-4">
                                <MudRadio T="EnableWhenBehavior ?" Value="null">None</MudRadio>
                                @foreach (var enumItem in Enum.GetValues<EnableWhenBehavior>())
                                {
                                    <MudRadio T="EnableWhenBehavior ?" Value="enumItem">
                                        @enumItem.ToString()
                                    </MudRadio>
                                }
                            </MudRadioGroup>
                        </div>

                        @if (_request.EnableBehavior != null)
                        {
                            <div>
                                <MudText Typo="Typo.subtitle2" Class="mb-2">Conditions</MudText>
                                @foreach (var enableWhenItem in _request.EnableWhen)
                                {
                                    <MudPaper Elevation="1" Class="pa-3 mb-2">
                                        <MudGrid>
                                            <MudItem xs="12" sm="4">
                                                <MudSelect Label="Question"
                                                           Clearable="true"
                                                           @bind-Value="enableWhenItem.LinkId">
                                                    @foreach (var questionnaireItem in _allItems.Where(s => s.LinkId != _request.LinkId))
                                                    {
                                                        <MudSelectItem Value="questionnaireItem.LinkId">
                                                            @questionnaireItem.Text
                                                        </MudSelectItem>
                                                    }
                                                </MudSelect>
                                            </MudItem>
                                            <MudItem xs="12" sm="3">
                                                <MudSelect T="Operator"
                                                           Label="Operator"
                                                           @bind-Value="enableWhenItem.Operator">
                                                    @foreach (var enumItem in Enum.GetValues<Operator>())
                                                    {
                                                        <MudSelectItem T="Operator" Value="enumItem">
                                                            @enumItem.ToString()
                                                        </MudSelectItem>
                                                    }
                                                </MudSelect>
                                            </MudItem>
                                            <MudItem xs="12" sm="4">
                                                @if (enableWhenItem.LinkId != null)
                                                {
                                                    var question = _allItems.First(s => s.LinkId == enableWhenItem.LinkId);

                                                    if (question.Options.Any())
                                                    {
                                                        <MudSelect T="QuestionAnswer ?"
                                                                   Label="Expected Option"
                                                                   @bind-Value="enableWhenItem.Answer">
                                                            @foreach (var option in question.Options)
                                                            {
                                                                <MudSelectItem T="QuestionAnswer ?" Value="option.Answer">
                                                                    @(option.Title ?? option.Answer?.Value)
                                                                </MudSelectItem>
                                                            }
                                                        </MudSelect>
                                                    }
                                                    else
                                                    {
                                                        <QuestionAnswerControl Type="question.Type.ToAnswerType()"
                                                                               @bind-Value="enableWhenItem.Answer"
                                                                               Label="Expected Value"/>
                                                    }
                                                }
                                            </MudItem>
                                            <MudItem xs="12" sm="1" Class="d-flex align-center justify-center">
                                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                               Color="Color.Error"
                                                               Size="Size.Small"
                                                               OnClick="@(_ => _request.EnableWhen.Remove(enableWhenItem))"/>
                                            </MudItem>
                                        </MudGrid>
                                    </MudPaper>
                                }

                                <MudButton StartIcon="@Icons.Material.Filled.Add"
                                           Variant="Variant.Outlined"
                                           Color="Color.Primary"
                                           OnClick="@(_ => _request.EnableWhen.Add(new EnableWhen()))">
                                    Add Condition
                                </MudButton>
                            </div>
                        }
                    </MudStack>
                </MudExpansionPanel>



                @if (_request.Type.IsAnswerableType())
                {
                    <!-- Answer Options Section -->
                    <MudExpansionPanel Text="@($"Answer Options ({_request.Options.Count})")">
                        <MudStack Spacing="2">
                            @if (_request.Options.Any())
                            {
                                <MudText Typo="Typo.subtitle2" Class="mb-2">Options</MudText>
                                @foreach (var option in _request.Options)
                                {
                                    <MudPaper Elevation="1" Class="pa-3">
                                        <MudGrid>
                                            <MudItem xs="12" sm="4">
                                                <QuestionAnswerControl @bind-Value="option.Answer" Label="Value"/>
                                            </MudItem>
                                            <MudItem xs="12" sm="5">
                                                <MudTextField @bind-Value="option.Title"
                                                              Immediate="true"
                                                              Label="Title"
                                                              HelperText="Option title/description"/>
                                            </MudItem>
                                            <MudItem xs="12" sm="2" Class="d-flex align-center">
                                                <MudCheckBox @bind-Value="option.InitialSelected">Initial selected</MudCheckBox>
                                            </MudItem>
                                            <MudItem xs="12" sm="1" Class="d-flex align-center justify-center">
                                                <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                               Color="Color.Error"
                                                               Size="Size.Small"
                                                               OnClick="@(_ => RemoveOption(option))"/>
                                            </MudItem>
                                        </MudGrid>
                                    </MudPaper>
                                }
                            }

                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Add"
                                       OnClick="@AddOption">
                                Add Option
                            </MudButton>

                            @if (!_request.Options.Any())
                            {
                                <div>
                                    <MudText Typo="Typo.subtitle2" Class="mb-2">Default Value</MudText>
                                    <QuestionAnswerControl Type="_request.Type.ToAnswerType()"
                                                           @bind-Value="_request.Initial"
                                                           Label="Initial value"/>
                                </div>
                            }
                        </MudStack>
                    </MudExpansionPanel>
                }

                <!-- Metadata Section -->
                <MetadataEditor @bind-Metadata="_request.Metadata" />
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Cancel</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Save
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }
    [Parameter] public QuestionnaireDto? Questionnaire { get; set; }
    [Parameter] public QuestionnaireItemDto? Data { get; set; }
    [Parameter] public QuestionnaireItemDto? ParentData { get; set; }

    private List<QuestionnaireItemDto> _allItems = [];

    private MudForm? _form;
    private bool _isEdit;
    private bool _loading;
    private Error? _error;

    private readonly SaveQuestionnaireItemRequest _request = new();
    private readonly SaveQuestionnaireItemRequestValidator _validator = new();

    protected override void OnInitialized()
    {
        _isEdit = Data is not null;
        _allItems = Questionnaire!.Items.Flatten(s => s.Items).ToList();
        _request.ParentQuestionnaireItemId = ParentData?.Id;

        if (_isEdit)
        {
            _request.Text = Data!.Text;
            _request.LinkId = Data.LinkId;
            _request.Type = Data.Type;
            _request.EnableWhen = Data.EnableWhen;
            _request.EnableBehavior = Data.EnableBehavior;
            _request.OrderNumber = Data.OrderNumber;
            _request.Required = Data.Required;
            _request.DisabledDisplay = Data.DisabledDisplay;
            _request.Prefix = Data.Prefix;
            _request.Repeats = Data.Repeats;
            _request.MaxLength = Data.MaxLength;
            _request.ReadOnly = Data.ReadOnly;
            _request.UiElementType = Data.UiElementType;
            _request.Options = Data.Options;
            _request.Initial = Data.Initial;
            _request.Metadata = Data.Metadata;
        }
        else
        {
            // For new items, set order number to be at the end
            if (ParentData != null)
            {
                // Adding to a parent item
                _request.OrderNumber = ParentData.Items.Count > 0 ? ParentData.Items.Max(x => x.OrderNumber) + 1 : 1;
            }
            else
            {
                // Adding to root level
                _request.OrderNumber = Questionnaire!.Items.Count > 0 ? Questionnaire.Items.Max(x => x.OrderNumber) + 1 : 1;
            }
        }

        // Ensure repeats availability is properly set based on current options
        UpdateRepeatsAvailability();
    }

    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;

        var result = _isEdit
            ? await QuestionnaireState.UpdateQuestionnaireItemAsync(Data!.Id, _request)
            : await QuestionnaireState.CreateQuestionnaireItemAsync(_request);

        if (result != null)
        {
            Snackbar.Add($"Question is {(_isEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(result));
        }
        else
        {
            _error = QuestionnaireState.Error;
        }

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();

    private void AddOption()
    {
        _request.Options.Add(new AnswerOption(new QuestionAnswer(null, _request.Type.ToAnswerType()), null, false));
        UpdateRepeatsAvailability();
    }

    private void RemoveOption(AnswerOption option)
    {
        _request.Options.Remove(option);
        UpdateRepeatsAvailability();
    }

    private void UpdateRepeatsAvailability()
    {
        // If there are 1 or fewer options, disable repeats functionality
        if (_request.Options.Count <= 1)
        {
            _request.Repeats = false;
        }
    }

    private bool IsUiElementTypeValid(UiElementType uiElementType)
    {
        return uiElementType switch
        {
            UiElementType.Default => true,
            UiElementType.Slider => _request.Type is QuestionType.Integer or QuestionType.Decimal && _request.Options.Any(),
            UiElementType.List => _request.Options.Any(),
            _ => false
        };
    }
}