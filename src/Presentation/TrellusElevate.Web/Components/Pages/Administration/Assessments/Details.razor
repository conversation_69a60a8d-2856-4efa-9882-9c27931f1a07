@page "/administration/assessments/{AssessmentId:guid}"
@using TrellusElevate.Application.Features.Assessments.Models
@using TrellusElevate.Application.Features.Assessments.Queries
@using TrellusElevate.Web.Common.Extensions

@inject IMediator Mediator
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>Assessments | Details</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Administration", href: "/administration", icon: Icons.Material.Filled.Settings),
                   new BreadcrumbItem("Assessments", href: "/administration/assessments", icon: Icons.Material.Filled.Assessment),
                   new BreadcrumbItem(_assessment?.Name!, href: $"/administration/assessments/{AssessmentId}", icon: Icons.Material.Filled.Assessment),
               ])">
    </MudBreadcrumbs>
</MudToolBar>

<MudGrid>
    <MudItem md="3">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Assessment info</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudStack>
                    <div>
                        <MudText Typo="Typo.body2">Name</MudText>
                        <MudText Typo="Typo.body1">@_assessment?.Name</MudText>
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">Description</MudText>
                        <MudText Typo="Typo.body1">@_assessment?.Description</MudText>
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">Status</MudText>
                        @if (_assessment != null)
                        {
                            <MudChip T="string" Color="@_assessment.Status.GetColor()">
                                @_assessment.Status.ToString()
                            </MudChip>
                        }
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">Created at</MudText>
                        <MudText Typo="Typo.body1">@_assessment?.CreatedAt.ToString("g")</MudText>
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">Updated at</MudText>
                        <MudText Typo="Typo.body1">@_assessment?.UpdatedAt?.ToString("g")</MudText>
                    </div>
                    <div>
                        <MudText Typo="Typo.body2">Questionnaire</MudText>
                        <MudText Typo="Typo.body1">@_assessment?.Questionnaire?.Title </MudText>
                    </div>
                </MudStack>
            </MudCardContent>
            <MudCardActions>
                <MudButton StartIcon="@Icons.Material.Filled.Edit"
                           OnClick="SaveAsync"
                           Variant="Variant.Filled"
                           Color="Color.Primary">
                    Edit
                </MudButton>
            </MudCardActions>
        </MudCard>
    </MudItem>
    <MudItem md="9">
        <MudCard>
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">Scoring profiles</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudDataGrid T="AssessmentScoringProfileDto" Items="_assessment?.AssessmentScoringProfiles ?? []"
                             Filterable="false"
                             Striped="true"
                             SortMode="SortMode.None">
                    <Columns>
                        <PropertyColumn Property="x => x.Name" Title="Name"/>
                        <PropertyColumn Property="x => x.Description" Title="Description"/>
                        <PropertyColumn Property="x => x.Status" Title="Status">
                            <CellTemplate>
                                <MudChip T="string" Color="@context.Item.Status.GetColor()">
                                    @context.Item.Status.ToString()
                                </MudChip>
                            </CellTemplate>
                        </PropertyColumn>
                        <PropertyColumn Property="x => x.CalculationFormula" Title="Formula"/>
                        <TemplateColumn>
                            <CellTemplate>
                                <MudButton StartIcon="@Icons.Material.Filled.Edit"
                                           OnClick="() => SaveScoringProfileAsync(context.Item)"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary">
                                    Edit profile
                                </MudButton>                                
                                <MudButton StartIcon="@Icons.Material.Filled.Map"
                                           OnClick="() => SaveScoreMappingsAsync(context.Item!)"
                                           Variant="Variant.Filled"
                                           Color="Color.Primary">
                                    Edit Mappings
                                </MudButton>
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                    <NoRecordsContent>
                        No records found
                    </NoRecordsContent>
                </MudDataGrid>
            </MudCardContent>
            <MudCardActions>
                <MudSpacer/>
                <MudButton StartIcon="@Icons.Material.Filled.Add"
                           OnClick="() => SaveScoringProfileAsync()"
                           Variant="Variant.Filled"
                           Color="Color.Primary">
                    Create new profile
                </MudButton>
            </MudCardActions>
        </MudCard>
    </MudItem>
</MudGrid>


@code {
    [Parameter] public Guid AssessmentId { get; set; }
    private AssessmentDto? _assessment;

    protected override async Task OnParametersSetAsync()
    {
        var result = await Mediator.Send(new GetAssessmentByIdQuery(AssessmentId));

        _assessment = result.Match(val => val, error =>
        {
            Snackbar.Add($"Error on load assessment: {error.Code}: {error.Description}", Severity.Error);
            return new AssessmentDto();
        });
    }


    private async Task SaveAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveAssessmentDialog> { { x => x.Data, _assessment } };

        var dialog = await DialogService.ShowAsync<SaveAssessmentDialog>("Update assessment", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            _assessment = (result.Data as AssessmentDto)!;
        }
    }

    private async Task SaveScoringProfileAsync(AssessmentScoringProfileDto? scoringProfile = null)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Large, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveScoringProfileDialog>
        {
            { x => x.Data, scoringProfile },
            { x => x.Assessment, _assessment }
        };

        var dialog = await DialogService.ShowAsync<SaveScoringProfileDialog>("Save scoring profile", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            var value = (result.Data as AssessmentScoringProfileDto)!;
            if (scoringProfile is null)
            {
                _assessment?.AssessmentScoringProfiles!.Add(value);
            }
            else
            {
                _assessment!.AssessmentScoringProfiles![_assessment.AssessmentScoringProfiles.FindIndex(s => s.Id == scoringProfile.Id)] = value;
            }
        }
    }
    
    
    private async Task SaveScoreMappingsAsync(AssessmentScoringProfileDto scoringProfile)
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Medium, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveScoreMappingsDialog>
        {
            { x => x.ScoringProfile, scoringProfile },
            { x => x.Assessment, _assessment }
        };

        var dialog = await DialogService.ShowAsync<SaveScoreMappingsDialog>("Save score mappings", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            scoringProfile.AssessmentScoreMappings = (result.Data as IReadOnlyList<AssessmentScoreMappingDto>)!.ToList();
        }
    }


}