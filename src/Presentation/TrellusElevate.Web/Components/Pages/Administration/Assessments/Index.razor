@page "/administration/assessments"
@using TrellusElevate.Application.Features.Assessments.Enums
@using TrellusElevate.Application.Features.Assessments.Models
@using TrellusElevate.Application.Features.Assessments.Queries
@using TrellusElevate.Core.Domains.Assessments.Entities
@using TrellusElevate.Web.Common.Extensions

@inject IMediator Mediator
@inject NavigationManager Nav
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>Assessments</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Administration", href: "/administration", icon: Icons.Material.Filled.Settings),
                   new BreadcrumbItem("Assessments", href: "/administration/assessments", icon: Icons.Material.Filled.Assessment)
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>
    <MudButton StartIcon="@Icons.Material.Filled.Add"
               OnClick="CreateAsync"
               Variant="Variant.Filled"
               Color="Color.Primary">
        New
    </MudButton>
</MudToolBar>

<MudDataGrid @ref="_dataGrid" T="AssessmentDto" ServerData="ServerReload" Filterable="false" Striped="true"
             Hover="true"
             SortMode="SortMode.Single"
             RowClick="@(row => Nav.NavigateTo($"/administration/assessments/{row.Item.Id}"))">
    <ToolBarContent>
        <MudStack Spacing="3" Row="true" Wrap="Wrap.Wrap">
            <MudTextField Class="pt-1"  
                          @bind-Value="_query.SearchTerm" T="string"
                          @bind-Value:after="Filter" Placeholder="Search"
                          Clearable="true"
                          Adornment="Adornment.Start"
                          AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium"/>

            <MudSelect   Dense="true" T="AssessmentStatus"
                       MultiSelectionTextFunc="@(val => string.Join(", ", val!.Select(s => Enum.Parse<AssessmentStatus>(s!).ToString())))"
                       Label="Status" MultiSelection="true" Clearable="true"
                       @bind-SelectedValues="_query.Status" @bind-SelectedValues:after="Filter">
                @foreach (var item in Enum.GetValues<AssessmentStatus>())
                {
                    <MudSelectItem T="AssessmentStatus" Value="item">@item.ToString()</MudSelectItem>
                }
            </MudSelect>

        </MudStack>
    </ToolBarContent>

    <Columns>
        <PropertyColumn Property="x => x.Name" Title="Name"/>
        <PropertyColumn Property="x => x.Status" Title="Status">
            <CellTemplate>
                <MudChip T="string" Color="@context.Item.Status.GetColor()">
                    @context.Item.Status.ToString()
                </MudChip>
            </CellTemplate>
        </PropertyColumn>
        <PropertyColumn Property="x => x.CreatedAt" Title="Created at" Format="g"/>
        <PropertyColumn Property="x => x.UpdatedAt" Title="Updated at" Format="g"/>
    </Columns>
    <PagerContent>
        <MudDataGridPager T="AssessmentDto"/>
    </PagerContent>
    <NoRecordsContent>
        No records found
    </NoRecordsContent>
</MudDataGrid>

@code {

    private MudDataGrid<AssessmentDto> _dataGrid = null!;
    readonly GetAssessmentListQuery _query = new();

    private async Task<GridData<AssessmentDto>> ServerReload(GridState<AssessmentDto> state)
    {
        _query.Page = state.Page + 1;
        _query.PageSize = state.PageSize;

        var sortDefinition = state.SortDefinitions.FirstOrDefault();
        if (sortDefinition != null)
        {
            _query.SortBy = Enum.Parse<AssessmentSort>(sortDefinition.SortBy);
            _query.SortDirection = sortDefinition.Descending ? Application.Common.Enums.SortDirection.Desc : Application.Common.Enums.SortDirection.Asc;
        }

        var result = await Mediator.Send(_query);

        return result.Match(
            value => new GridData<AssessmentDto>
            {
                TotalItems = value.TotalCount,
                Items = value.Items
            },
            error =>
            {
                Snackbar.Add($"Error loading questionnaires: {error.Code}: {error.Description}", Severity.Error);
                return new GridData<AssessmentDto>();
            });
    }

    private async Task CreateAsync()
    {
        var options = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true, CloseOnEscapeKey = true };
        var parameters = new DialogParameters<SaveAssessmentDialog> { { x => x.Data, null } };

        var dialog = await DialogService.ShowAsync<SaveAssessmentDialog>("Create assessment", parameters, options);

        var result = await dialog.Result;

        if (!result!.Canceled)
        {
            Nav.NavigateTo($"/administration/assessments/{(result.Data as AssessmentDto)?.Id}");
        }
    }


    private Task Filter()
    {
        return _dataGrid.ReloadServerData();
    }

}