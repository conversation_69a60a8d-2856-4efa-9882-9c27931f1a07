@using TrellusElevate.Application.Features.Assessments.Models
@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Application.Features.Questionnaires.Queries
@using TrellusElevate.Core.Domains.Assessments.Entities
@using TrellusElevate.Presentation.Features.Assessments.Mappers
@using TrellusElevate.Presentation.Features.Assessments.Models
@using Error = TrellusElevate.Core.Common.Errors.Error
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog!.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudTextField   @bind-Value="_request.Name"
                              For="@(() => _request.Name)"
                              Immediate="true"
                              Label="Name"/>
                
                <MudTextField   @bind-Value="_request.Description"
                              For="@(() => _request.Description)"
                              Lines="2"
                              Immediate="true"
                              Label="Description"/>
                
                @if (_isEdit)
                {
                    <MudSelect   Dense="true"
                               T="AssessmentStatus"
                               Label="Status"
                               @bind-Value="_request.Status">
                        @foreach (var item in Enum.GetValues<AssessmentStatus>())
                        {
                            <MudSelectItem T="AssessmentStatus" Value="item">
                                @item.ToString()
                            </MudSelectItem>
                        }
                    </MudSelect>
                }

                <MudAutocomplete   Dense="true" T="Guid"
                                 Label="Questionnaire"
                                 For="@(() => _request.QuestionnaireId)"
                                 @bind-Value="_request.QuestionnaireId"
                                 DebounceInterval="300"
                                 SearchFunc="@SearchQuestionnaires"
                                 SelectOnActivation="false"
                                 ResetValueOnEmptyText="true"
                                 ShowProgressIndicator="true"
                                 ToStringFunc="@(c => _questionnaires.GetValueOrDefault(c)?.Title)"/>
            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Cancel</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary" Variant="Variant.Filled" OnClick="Submit">Save</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public AssessmentDto? Data { get; set; }

    private MudForm? _form;
    private bool _isEdit;
    private bool _loading;
    private Error? _error;

    private readonly SaveAssessmentRequest _request = new();
    private readonly SaveAssessmentRequestValidator _validator = new();

    private Dictionary<Guid, QuestionnaireListItemDto> _questionnaires = new();

    protected override void OnInitialized()
    {
        _isEdit = Data is not null;
        if (_isEdit)
        {
            _request.Name = Data!.Name;
            _request.Description = Data.Description;
            _request.Status = Data.Status;
            _request.QuestionnaireId = Data.QuestionnaireId;
            _questionnaires.TryAdd(_request.QuestionnaireId, new QuestionnaireListItemDto
            {
                Id = Data.Questionnaire!.Id,
                Title = Data.Questionnaire.Title
            });
        }
    }
    
    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            _isEdit ? await Mediator.Send(_request.ToCommand(Data!.Id)) : await Mediator.Send(_request.ToCommand());

        result.Switch(value =>
        {
            Snackbar.Add($"Assessment {result.Value.Name} is {(_isEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();

    private async Task<IEnumerable<Guid>> SearchQuestionnaires(string searchTerm, CancellationToken cancellationToken)
    {
        var result = await Mediator.Send(new GetQuestionnaireListQuery { SearchTerm = searchTerm }, cancellationToken);

        _questionnaires = result.Match(
            value => value.Items.ToDictionary(s => s.Id),
            error =>
            {
                _error = error;
                return new Dictionary<Guid, QuestionnaireListItemDto>();
            });
        
        return _questionnaires.Keys;
    }

}