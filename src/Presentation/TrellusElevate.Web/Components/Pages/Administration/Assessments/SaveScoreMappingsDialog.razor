@using TrellusElevate.Application.Common.Extensions
@using TrellusElevate.Application.Features.Assessments.Commands
@using TrellusElevate.Application.Features.Assessments.Models
@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Core.Domains.Questionnaires.Common.Helpers
@using TrellusElevate.Core.Domains.Questionnaires.Entities
@using TrellusElevate.Core.Domains.Questionnaires.Mappers
@using TrellusElevate.Core.Domains.Questionnaires.ValueObjects
@using TrellusElevate.Presentation.Features.Assessments.Models
@using TrellusElevate.Web.Components.Pages.Administration.Questionnaires
@using Error = TrellusElevate.Core.Common.Errors.Error
@inject ISnackbar Snackbar
@inject IMediator Mediator
@inject IDialogService DialogService

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog!.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }

        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudSimpleTable>
                <tbody>
                @for (var index = 0; index < _request.Mappings.Count; index++)
                {
                    var currentIndex = index;
                    var mapping = _request.Mappings[index];
                    <tr>
                        <td colspan="5">
                            <MudForm Model="@mapping" Validation="@_mappingValidator.AsMudFormValidation()">
                                <div style="display: flex; width: 100%; align-items: center;">
                                    <!-- Info Icon Column -->
                                    <div style="flex: 0 0 auto; padding-right: 8px; display: flex; align-items: center;">
                                        @{
                                            var questionItem = _questionnaireItems.GetValueOrDefault(mapping.QuestionnaireItemId);
                                            var tooltipText = questionItem?.Text ?? "No question selected";
                                        }
                                        <MudTooltip Text="@tooltipText" Placement="Placement.Top">
                                            <MudIcon Icon="@Icons.Material.Filled.Info"
                                                     Color="Color.Info"
                                                     Size="Size.Small"/>
                                        </MudTooltip>
                                    </div>

                                    <!-- Question Selection Column -->
                                    <div style="flex: 1; padding-right: 8px;">
                                        <MudSelect T="Guid"
                                                   Dense="true"
                                                   Label="Question"
                                                   @bind-Value="mapping.QuestionnaireItemId"
                                                   For="@(() => mapping.QuestionnaireItemId)">
                                            @foreach (var questionnaireItem in _questionnaireItems.Values)
                                            {
                                                <MudSelectItem T="Guid" Value="questionnaireItem.Id">
                                                    @questionnaireItem.LinkId
                                                </MudSelectItem>
                                            }
                                        </MudSelect>
                                    </div>
                                    <!-- Operator Column -->
                                    <div style="flex: 1; padding-right: 8px;">
                                        <MudSelect Label="Operator"
                                                   Dense="true"
                                                   T="Operator?"
                                                   @bind-Value="mapping.Operator"
                                                   For="@(() => mapping.Operator)">
                                            @foreach (var enumItem in Enum.GetValues<Operator>())
                                            {
                                                <MudSelectItem T="Operator?" Value="enumItem">
                                                    @enumItem.ToString()
                                                </MudSelectItem>
                                            }
                                        </MudSelect>
                                    </div>

                                    <!-- Answer/Value Column -->
                                    <div style="flex: 1; padding-right: 8px;">
                                        @if (mapping.QuestionnaireItemId != Guid.Empty && _questionnaireItems.GetValueOrDefault(mapping.QuestionnaireItemId)?.Options.Any() == true)
                                        {
                                            var mappingQuestionItem = _questionnaireItems.GetValueOrDefault(mapping.QuestionnaireItemId);
                                            var isMultiSelect = mappingQuestionItem?.Repeats == true;

                                            @if (isMultiSelect)
                                            {
                                                <MudSelect Label="Options"
                                                           T="QuestionAnswer"
                                                           Clearable="true"
                                                           MultiSelection="true"
                                                           SelectedValues="@mapping.Answers"
                                                           SelectedValuesChanged="@(values => SetMappingAnswers(mapping, values))"
                                                           ToStringFunc="@(answer => GetOptionTitle(answer, mappingQuestionItem!))"
                                                           MultiSelectionTextFunc="@(selectedValues => GetMultiSelectionText(selectedValues, mappingQuestionItem!))">
                                                    @foreach (var option in mappingQuestionItem!.Options)
                                                    {
                                                        <MudSelectItem T="QuestionAnswer?" Value="@option.Answer">
                                                            @(option.Title ?? option.Answer?.Value)
                                                        </MudSelectItem>
                                                    }
                                                </MudSelect>
                                            }
                                            else
                                            {
                                                <MudSelect Label="Option"
                                                           Dense="true"
                                                           T="QuestionAnswer?"
                                                           Value="@(mapping.Answers.FirstOrDefault())"
                                                           ValueChanged="@(value => SetSingleMappingAnswer(mapping, value))">
                                                    @foreach (var option in mappingQuestionItem!.Options)
                                                    {
                                                        <MudSelectItem T="QuestionAnswer?" Value="option.Answer">
                                                            @(option.Title ?? option.Answer?.Value)
                                                        </MudSelectItem>
                                                    }
                                                </MudSelect>
                                            }
                                        }
                                        else
                                        {
                                            <QuestionAnswerControl
                                                Value="@(mapping.Answers.FirstOrDefault())"
                                                ValueChanged="@(value => SetSingleMappingAnswer(mapping, value))"
                                                Type="_questionnaireItems.GetValueOrDefault(mapping.QuestionnaireItemId)?.Type.ToAnswerType() ?? AnswerType.String"
                                                Label="Value"/>
                                        }
                                    </div>

                                    <!-- Weight Column -->
                                    <div style="flex: 1; padding-right: 8px;">
                                        <MudNumericField @bind-Value="mapping.Weight"
                                                         Label="Weight"
                                                         For="@(() => mapping.Weight)"/>
                                    </div>

                                    <!-- Actions Column -->
                                    <div style="flex: 0 0 auto; display: flex; align-items: center; gap: 4px;">
                                        <MudIconButton Icon="@Icons.Material.Filled.Remove"
                                                       Color="Color.Error"
                                                       Size="Size.Small"
                                                       OnClick="@(_ => RemoveMapping(currentIndex))"/>
                                        <MudIconButton Icon="@Icons.Material.Filled.Add"
                                                       Color="Color.Primary"
                                                       Size="Size.Small"
                                                       OnClick="@(_ => AddMappingAt(currentIndex))"/>
                                    </div>
                                </div>
                            </MudForm>
                        </td>
                    </tr>
                }
                </tbody>
            </MudSimpleTable>


        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Cancel</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary"
                   Variant="Variant.Filled" OnClick="Submit">Save
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public AssessmentDto? Assessment { get; set; }
    [Parameter] public AssessmentScoringProfileDto? ScoringProfile { get; set; }

    private Dictionary<Guid, QuestionnaireItemDto> _questionnaireItems = [];

    private MudForm? _form;
    private bool _loading;
    private Error? _error;

    private readonly SaveAssessmentScoreMappingRequest _request = new();
    private readonly SaveAssessmentScoreMappingRequestValidator _validator = new();
    private readonly AssessmentScoreMappingValidator _mappingValidator = new();

    protected override void OnInitialized()
    {
        var questionnaireItems = Assessment!.Questionnaire!
            .Items
            .Flatten(s => s.Items)
            .Where(x => x.Type.IsAnswerableType())
            .OrderBy(s => s.OrderNumber)
            .ToList();

        _questionnaireItems = questionnaireItems.ToDictionary(x => x.Id);

        if (ScoringProfile?.AssessmentScoreMappings.Count > 0)
        {
            _request.Mappings = ScoringProfile.AssessmentScoreMappings.ToList();
        }
        else
        {
            _request.Mappings = [];
            foreach (var q in questionnaireItems)
            {
                if (!q.Options.Any())
                {
                    _request.Mappings.Add(new AssessmentScoreMappingDto
                    {
                        QuestionnaireItemId = q.Id,
                        Operator = Operator.Equals,
                        Answers = []
                    });
                }
                else
                {
                    _request.Mappings.AddRange(q.Options.Select(s => new AssessmentScoreMappingDto
                    {
                        QuestionnaireItemId = q.Id,
                        Operator = Operator.Equals,
                        Answers = s.Answer != null ? [s.Answer] : []
                    }));
                }
            }
        }

        _request.Mappings = _request.Mappings.OrderBy(s => s.QuestionnaireItemId).ToList();
    }

    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        var command = new SaveAssessmentScoreMappingsCommand(Assessment!.Id,
            ScoringProfile!.Id,
            _request.Mappings);

        _loading = true;
        var result = await Mediator.Send(command);

        result.Switch(value =>
        {
            Snackbar.Add("Score Mappings saved", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();

    private void SetMappingAnswers(AssessmentScoreMappingDto mapping, IEnumerable<QuestionAnswer?>? values)
    {
        mapping.Answers = values.ToList();
    }

    private void SetSingleMappingAnswer(AssessmentScoreMappingDto mapping, QuestionAnswer? value)
    {
        mapping.Answers = value != null ? [value] : [];
    }

    private string GetMultiSelectionText(List<string>? selectedStringValues, QuestionnaireItemDto questionItem)
    {
        if (selectedStringValues == null || !selectedStringValues.Any())
            return "";

        return selectedStringValues.Count switch
        {
            1 => selectedStringValues.First(),
            2 => string.Join(", ", selectedStringValues),
            3 => string.Join(", ", selectedStringValues),
            _ => $"{string.Join(", ", selectedStringValues.Take(2))}, +{selectedStringValues.Count - 2} more"
        };
    }

    private string GetOptionTitle(QuestionAnswer? answer, QuestionnaireItemDto questionItem)
    {
        if (answer == null) return "";

        var option = questionItem.Options.FirstOrDefault(o => o.Answer?.Equals(answer) == true);
        return option?.Title ?? "";
    }

    private async Task RemoveMapping(int index)
    {
        bool? dialogResult = await DialogService.ShowMessageBox(
            "Warning",
            "Are you sure?",
            yesText: "Yes, continue", cancelText: "No, cancel");

        if (dialogResult != true) return;
        
        if (index < 0 || index >= _request.Mappings.Count) return;
        _request.Mappings.RemoveAt(index);
        StateHasChanged();
    }

    private void AddMappingAt(int index)
    {
        if (index < 0 || index > _request.Mappings.Count) return;
        var mapping = _request.Mappings[index];
        _request.Mappings.Insert(index + 1, new AssessmentScoreMappingDto
        {
            QuestionnaireItemId = mapping.QuestionnaireItemId,
            Operator = mapping.Operator,
            Answers = []
        });
        StateHasChanged();
    }

}