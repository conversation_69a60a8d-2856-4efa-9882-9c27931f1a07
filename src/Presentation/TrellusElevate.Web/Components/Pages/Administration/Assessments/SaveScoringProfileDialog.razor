@using TrellusElevate.Application.Common.Extensions
@using TrellusElevate.Application.Features.Assessments.Models
@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Core.Domains.Assessments.Entities
@using TrellusElevate.Core.Domains.Questionnaires.Common.Helpers
@using TrellusElevate.Presentation.Features.Assessments.Mappers
@using TrellusElevate.Presentation.Features.Assessments.Models
@using TrellusElevate.Core.Common
@using Error = TrellusElevate.Core.Common.Errors.Error
@inject ISnackbar Snackbar
@inject IMediator Mediator

@if (_loading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate="true"/>
}
<MudDialog>
    <TitleContent>
        @MudDialog!.Title
    </TitleContent>
    <DialogContent>
        @if (_error is not null)
        {
            <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Dense="true" ShowCloseIcon="true"
                      CloseIconClicked="() => { _error = null; }">
                @_error?.Code: @_error?.Description
            </MudAlert>
        }
        <MudForm Model="@_request" @ref="@_form"
                 Validation="@_validator.AsMudFormValidation()"
                 ValidationDelay="0">
            <MudStack Spacing="3">
                <MudTextField   @bind-Value="_request.Name"
                              For="@(() => _request.Name)"
                              Immediate="true"
                              Label="Name"/>
                
                <MudTextField   @bind-Value="_request.Description"
                              For="@(() => _request.Description)"
                              Lines="2"
                              Immediate="true"
                              Label="Description"/>
                
                @if (_isEdit)
                {
                    <MudSelect T="AssessmentScoringProfileStatus"
                               Label="Status"
                               @bind-Value="_request.Status">
                        @foreach (var item in Enum.GetValues<AssessmentScoringProfileStatus>())
                        {
                            <MudSelectItem T="AssessmentScoringProfileStatus" Value="item">
                                @item.ToString()
                            </MudSelectItem>
                        }
                    </MudSelect>

                    <!-- Formula Construction Section -->
                    <MudExpansionPanel Text="Formula Builder">
                        <MudStack Spacing="3">
                            <!-- Quick Formula Templates -->
                            <div>
                                <MudText Typo="Typo.subtitle2" Class="mb-2">Quick Templates</MudText>
                                <MudStack Row Spacing="2" Wrap="Wrap.Wrap">
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.Functions"
                                               OnClick="CreateSumOfAllFormula">
                                        Sum of All
                                    </MudButton>
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Secondary"
                                               StartIcon="@Icons.Material.Filled.Calculate"
                                               OnClick="CreateAverageOfAllFormula">
                                        Average of All
                                    </MudButton>
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Tertiary"
                                               StartIcon="@Icons.Material.Filled.Balance"
                                               OnClick="CreateWeightedSumFormula">
                                        Weighted Sum
                                    </MudButton>
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Warning"
                                               StartIcon="@Icons.Material.Filled.Clear"
                                               OnClick="ClearFormula">
                                        Clear
                                    </MudButton>
                                </MudStack>
                            </div>

                            <!-- Formula Input with Validation -->
                            <div>
                                <MudStack Row Spacing="2">
                                    <MudTextField @bind-Value="_request.CalculationFormula"
                                                  @ref="_formulaTextField"
                                                  For="@(() => _request.CalculationFormula)"
                                                  Lines="4"
                                                  Immediate="true"
                                                  Label="Calculation Formula"
                                                  HelperText="Use NCalc syntax. Click question chips or operators to build your formula."
                                                  Variant="Variant.Outlined"
                                                  Style="flex: 1;"/>
                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Info"
                                               StartIcon="@Icons.Material.Filled.CheckCircle"
                                               OnClick="ValidateFormula"
                                               Style="align-self: flex-start; margin-top: 8px;">
                                        Validate
                                    </MudButton>
                                </MudStack>

                                @if (!string.IsNullOrEmpty(_request.CalculationFormula))
                                {
                                    <div class="mt-2">
                                        @if (_formulaValidationResult?.IsOk == true)
                                        {
                                            <MudAlert Severity="Severity.Success" Dense="true" NoIcon="false">
                                                <strong>Valid Formula</strong> - Ready to calculate scores
                                            </MudAlert>
                                        }
                                        else if (_formulaValidationResult?.IsError == true)
                                        {
                                            <MudAlert Severity="Severity.Error" Dense="true" NoIcon="false" ShowCloseIcon="true" OnClick="@(() => _formulaValidationResult = null)">
                                                <strong>Invalid Formula:</strong> @_formulaValidationResult.Error.Description
                                            </MudAlert>
                                        }
                                    </div>
                                }
                            </div>

                            <!-- Operator Buttons -->
                            <div>
                                <MudText Typo="Typo.subtitle2" Class="mb-2">Operators</MudText>
                                <MudStack Row Spacing="1" Wrap="Wrap.Wrap">
                                    <MudButton Variant="Variant.Text"
                                               Size="Size.Small"
                                               OnClick="@(() => InsertAtCursor("+"))">+</MudButton>
                                    <MudButton Variant="Variant.Text"
                                               Size="Size.Small"
                                               OnClick="@(() => InsertAtCursor("-"))">-</MudButton>
                                    <MudButton Variant="Variant.Text"
                                               Size="Size.Small"
                                               OnClick="@(() => InsertAtCursor("*"))">&times;</MudButton>
                                    <MudButton Variant="Variant.Text"
                                               Size="Size.Small"
                                               OnClick="@(() => InsertAtCursor("/"))">&divide;</MudButton>
                                    <MudButton Variant="Variant.Text"
                                               Size="Size.Small"
                                               OnClick="@(() => InsertAtCursor("("))">(</MudButton>
                                    <MudButton Variant="Variant.Text"
                                               Size="Size.Small"
                                               OnClick="@(() => InsertAtCursor(")"))">)</MudButton>
                                    <MudButton Variant="Variant.Text"
                                               Size="Size.Small"
                                               OnClick="@(() => InsertAtCursor("Max("))">Max</MudButton>
                                    <MudButton Variant="Variant.Text"
                                               Size="Size.Small"
                                               OnClick="@(() => InsertAtCursor("Min("))">Min</MudButton>
                                    <MudButton Variant="Variant.Text"
                                               Size="Size.Small"
                                               OnClick="@(() => InsertAtCursor("Abs("))">Abs</MudButton>
                                </MudStack>
                            </div>

                            <!-- Available Questions -->
                            <div>
                                <MudText Typo="Typo.subtitle2" Class="mb-2">Available Questions</MudText>
                                <MudStack Row Wrap="Wrap.Wrap" Spacing="1">
                                    @foreach (var mapping in Data!.AssessmentScoreMappings.DistinctBy(s => s.QuestionnaireItemId))
                                    {
                                        var questionItem = _questionnaireItems[mapping.QuestionnaireItemId];
                                        <MudTooltip Text="@questionItem.Text" Placement="Placement.Top">
                                            <MudChip T="string"
                                                     Color="Color.Info"
                                                     Size="Size.Small"
                                                     OnClick="@(() => InsertAtCursor($"[{questionItem.LinkId}]"))">
                                                @questionItem.LinkId
                                            </MudChip>
                                        </MudTooltip>
                                    }
                                </MudStack>
                            </div>

                            <!-- Formula Examples -->
                            <div>
                                <MudText Typo="Typo.subtitle2" Class="mb-2">Examples</MudText>
                                <MudStack Spacing="1">
                                    <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                        <strong>Sum:</strong> [Q1]+[Q2]+[Q3]
                                    </MudText>
                                    <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                        <strong>Average:</strong> ([Q1]+[Q2]+[Q3])/3
                                    </MudText>
                                    <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                        <strong>Weighted:</strong> [Q1]*0.5+[Q2]*0.3+[Q3]*0.2
                                    </MudText>
                                    <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                        <strong>Conditional:</strong> if([Q1] > 5, [Q2] * 2, [Q2])
                                    </MudText>
                                </MudStack>
                            </div>
                        </MudStack>
                    </MudExpansionPanel>
                }

            </MudStack>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton StartIcon="@Icons.Material.Filled.Cancel" Disabled="_loading" OnClick="Cancel">Cancel</MudButton>
        <MudButton StartIcon="@Icons.Material.Filled.Save" Disabled="_loading" Color="Color.Primary" Variant="Variant.Filled" OnClick="Submit">Save</MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter] private IMudDialogInstance? MudDialog { get; set; }

    [Parameter] public AssessmentDto? Assessment { get; set; }
    [Parameter] public AssessmentScoringProfileDto? Data { get; set; }

    private Dictionary<Guid, QuestionnaireItemDto> _questionnaireItems = [];

    private MudForm? _form;
    private MudTextField<string>? _formulaTextField;
    private bool _isEdit;
    private bool _loading;
    private Error? _error;
    private Result? _formulaValidationResult;

    private readonly SaveAssessmentScoringProfileRequest _request = new();
    private readonly SaveAssessmentScoringProfileRequestValidator _validator = new();

    protected override void OnInitialized()
    {
        _isEdit = Data is not null;
        if (_isEdit)
        {
            _request.Name = Data!.Name;
            _request.Description = Data.Description;
            _request.Status = Data.Status;
            _request.CalculationFormula = Data.CalculationFormula;

            _questionnaireItems = Assessment!.Questionnaire!.Items
                .Flatten(s => s.Items)
                .Where(x => x.Type.IsAnswerableType())
                .OrderBy(s => s.OrderNumber)
                .ToList()
                .ToDictionary(s => s.Id);
        }
    }
    
    private async Task Submit()
    {
        await _form!.Validate();

        if (!_form.IsValid) return;

        _loading = true;
        var result =
            _isEdit ? await Mediator.Send(_request.ToCommand(Assessment!.Id, Data!.Id)) : await Mediator.Send(_request.ToCommand(Assessment!.Id));

        result.Switch(value =>
        {
            Snackbar.Add($"Scoring profile {result.Value.Name} is {(_isEdit ? "updated" : "created")}!", Severity.Success);
            MudDialog!.Close(DialogResult.Ok(value));
        }, error => _error = error);

        _loading = false;
    }

    private void Cancel() => MudDialog!.Cancel();

    private void CreateSumOfAllFormula()
    {
        var questionIds = Data!.AssessmentScoreMappings
            .DistinctBy(s => s.QuestionnaireItemId)
            .Select(m => _questionnaireItems[m.QuestionnaireItemId].LinkId)
            .ToList();

        if (questionIds.Any())
        {
            _request.CalculationFormula = string.Join("+", questionIds.Select(id => $"[{id}]"));
        }
    }

    private void CreateAverageOfAllFormula()
    {
        var questionIds = Data!.AssessmentScoreMappings
            .DistinctBy(s => s.QuestionnaireItemId)
            .Select(m => _questionnaireItems[m.QuestionnaireItemId].LinkId)
            .ToList();

        if (questionIds.Any())
        {
            var sumFormula = string.Join("+", questionIds.Select(id => $"[{id}]"));
            _request.CalculationFormula = $"({sumFormula})/{questionIds.Count}";
        }
    }

    private void CreateWeightedSumFormula()
    {
        var questionIds = Data!.AssessmentScoreMappings
            .DistinctBy(s => s.QuestionnaireItemId)
            .Select(m => _questionnaireItems[m.QuestionnaireItemId].LinkId)
            .ToList();

        if (questionIds.Any())
        {
            // Create a weighted sum with equal weights that sum to 1
            var weight = Math.Round(1.0 / questionIds.Count, 2);
            _request.CalculationFormula = string.Join("+", questionIds.Select(id => $"[{id}]*{weight}"));
        }
    }

    private void ClearFormula()
    {
        _request.CalculationFormula = string.Empty;
        _formulaValidationResult = null;
    }

    private void InsertAtCursor(string text)
    {
        // For now, just append to the end. In a more advanced implementation,
        // we could track cursor position and insert at the actual cursor location
        _request.CalculationFormula += text;
    }

    private void ValidateFormula()
    {
        if (string.IsNullOrWhiteSpace(_request.CalculationFormula))
        {
            _formulaValidationResult = null;
            return;
        }

        try
        {
            var expression = new NCalc.Expression(_request.CalculationFormula);
            _formulaValidationResult = expression.HasErrors()
                ? Error.Validation("Formula.SyntaxError", $"Invalid formula")
                : Result.Ok();
        }
        catch (Exception)
        {
            _formulaValidationResult = Error.Validation("Formula.InvalidFormula", "Formula has invalid structure");
        }
    }

}