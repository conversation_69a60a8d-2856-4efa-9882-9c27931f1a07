@inherits LayoutComponentBase
@layout Layout.MainLayout

@Body

<MudDrawer Style="top: 0" Class="mt-10 pt-4" @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Variant="@DrawerVariant.Mini">
    <MudNavMenu>
        <MudNavLink Href="administration/questionnaires" Match="NavLinkMatch.Prefix"
                    Icon="@Icons.Material.Filled.QuestionMark">Questionnaires
        </MudNavLink>
        <MudNavLink Href="administration/assessments" Match="NavLinkMatch.Prefix"
                    Icon="@Icons.Material.Filled.Assessment">Assessments
        </MudNavLink>
    </MudNavMenu>
    <MudSpacer/>
    <MudStack Row>
        <MudSpacer/>
        <MudToggleIconButton
            ToggledChanged="@(_ => _drawerOpen = !_drawerOpen)"
            Toggled="_drawerOpen"
            Size="Size.Small"
            Icon="@Icons.Material.Filled.ArrowForward"
            ToggledIcon="@Icons.Material.Filled.ArrowBack"
            Color="Color.Inherit" Edge="Edge.Start"/>
    </MudStack>
</MudDrawer>

@code {
    private bool _drawerOpen;
}