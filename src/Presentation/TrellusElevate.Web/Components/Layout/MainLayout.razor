@inherits LayoutComponentBase

<MudThemeProvider Theme="@_theme" IsDarkMode="_isDarkMode"/>
<MudPopoverProvider/>
<MudDialogProvider/>
<MudSnackbarProvider/>

<MudLayout>
    <MudAppBar Dense="true" Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start"
                       OnClick="@(_ => _drawerOpen = !_drawerOpen)" Class="mr-2 d-flex d-md-none"/>
        <MudText Typo="Typo.h6" Class="ml-3">Trellus Elevate®</MudText>
        <MudSpacer/>

        <div class="d-none d-md-flex">
            <MudNavLink Href="/" IconColor="Color.Inherit" ActiveClass="border-t-4"
                        Icon="@Icons.Material.Filled.Home" Match="NavLinkMatch.All">Home
            </MudNavLink>
            <MudNavLink Href="/patients" IconColor="Color.Inherit" ActiveClass="border-t-4"
                        Icon="@Icons.Material.Filled.Person2" Match="NavLinkMatch.Prefix">Patients
            </MudNavLink>
            <MudNavLink Href="/practitioners" IconColor="Color.Inherit" ActiveClass="border-t-4"
                        Icon="@Icons.Material.Filled.Person" Match="NavLinkMatch.Prefix">Providers
            </MudNavLink>
            <MudNavLink Href="/clients" IconColor="Color.Inherit" ActiveClass="border-t-4"
                        Icon="@Icons.Material.Filled.AccountBalance" Match="NavLinkMatch.Prefix">Clients
            </MudNavLink>
            <MudNavLink Href="/administration" IconColor="Color.Inherit" ActiveClass="border-t-4"
                        Icon="@Icons.Material.Filled.Settings" Match="NavLinkMatch.Prefix">Administration
            </MudNavLink>
        </div>
        
        <MudSpacer/>
        <MudIconButton Icon="@(DarkLightModeButtonIcon)" Color="Color.Inherit" OnClick="() => _isDarkMode = !_isDarkMode"/>
        <MudAvatar Color="Color.Info">FD</MudAvatar>
    </MudAppBar>

    <!-- Mobile navigation drawer - only shows on small screens -->
    <MudDrawer @bind-Open="_drawerOpen" Elevation="1" Variant="@DrawerVariant.Temporary"
               ClipMode="DrawerClipMode.Always">
        <MudDrawerHeader>
            <MudText Typo="Typo.h6" Class="ml-3">Trellus Elevate®</MudText>
        </MudDrawerHeader>
        <MudNavMenu>
            <MudNavLink Href="/" IconColor="Color.Inherit"
                        Icon="@Icons.Material.Filled.Home" Match="NavLinkMatch.All">Home
            </MudNavLink>
            <MudNavLink Href="/patients" IconColor="Color.Inherit"
                        Icon="@Icons.Material.Filled.Person2" Match="NavLinkMatch.Prefix">Patients
            </MudNavLink>
            <MudNavLink Href="/practitioners" IconColor="Color.Inherit"
                        Icon="@Icons.Material.Filled.Person" Match="NavLinkMatch.Prefix">Providers
            </MudNavLink>
            <MudNavLink Href="/clients" IconColor="Color.Inherit"
                        Icon="@Icons.Material.Filled.AccountBalance" Match="NavLinkMatch.Prefix">Clients
            </MudNavLink>
            <MudNavLink Href="/administration" IconColor="Color.Inherit"
                        Icon="@Icons.Material.Filled.Settings" Match="NavLinkMatch.Prefix">Administration
            </MudNavLink>
        </MudNavMenu>
    </MudDrawer>
    
    <MudMainContent Class="mt-10 mb-3 pa-4">
        @Body
    </MudMainContent>
    
    <MudAppBar Style="height: 24px" Bottom="true" Dense="true" Elevation="1">
        <MudSpacer/>
        <MudText Typo="Typo.body2" Class="mx-auto">©@DateTime.Now.Year Trellus Elevate®</MudText>
        <MudSpacer/>
        <MudText Typo="Typo.body2" Class="mx-auto">v2.0</MudText>
    </MudAppBar>
    
</MudLayout>

@code {
    private bool _isDarkMode;
    private bool _drawerOpen;
    private readonly MudTheme? _theme = Theme.MainTheme;

    private string DarkLightModeButtonIcon => _isDarkMode
        ? Icons.Material.Rounded.AutoMode // Light mode icon
        : Icons.Material.Outlined.DarkMode; // Dark mode icon

}