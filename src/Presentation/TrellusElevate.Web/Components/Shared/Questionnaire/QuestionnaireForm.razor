@using TrellusElevate.Application.Features.QuestionnaireResponses.Models
@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Application.Features.QuestionnaireResponses.Mappers
@using TrellusElevate.Application.Common.Extensions

<MudForm @ref="_form" @bind-IsValid="@IsFormValid">
    <MudStack Spacing="4">
        @if (Questionnaire?.Items != null)
        {
            @foreach (var topLevelItem in Questionnaire.Items.OrderBy(x => x.OrderNumber))
            {
                var requestItem = QuestionnaireResponse?.Items?.FirstOrDefault(r => r.QuestionnaireItemId == topLevelItem.Id);
                @if (requestItem != null)
                {
                    <QuestionItemControl Item="topLevelItem"
                                         RequestItem="requestItem"
                                         Disabled="@Disabled"
                                         AllResponseItems="@(QuestionnaireResponse?.Items ?? [])"
                                         AllQuestionnaireItems="@_allQuestionnaireItems"
                                         OnAnswerChanged="@HandleAnswerChanged"/>
                }
            }
        }
    </MudStack>
</MudForm>

@code {
    [Parameter] public QuestionnaireDto? Questionnaire { get; set; }
    [Parameter] public SaveQuestionnaireResponseDto? QuestionnaireResponse { get; set; }
    [Parameter] public EventCallback<SaveQuestionnaireResponseDto> QuestionnaireResponseChanged { get; set; }
    [Parameter] public bool Disabled { get; set; }
    [Parameter] public EventCallback<bool> IsFormValidChanged { get; set; }
    [Parameter] public EventCallback OnAnswerChanged { get; set; }

    private MudForm _form = null!;
    private List<QuestionnaireItemDto> _allQuestionnaireItems = [];
    private bool _isFormValid;

    public bool IsFormValid
    {
        get => _isFormValid;
        private set
        {
            if (_isFormValid != value)
            {
                _isFormValid = value;
                IsFormValidChanged.InvokeAsync(value);
            }
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (Questionnaire != null)
        {
            _allQuestionnaireItems = Questionnaire.Items
                .Flatten(s => s.Items)
                .ToList();
            
            await InitializeQuestionnaireResponse();
        }
    }

    private async Task InitializeQuestionnaireResponse()
    {
        if (Questionnaire == null) return;

        // Initialize response items from questionnaire structure if not already set
        if (QuestionnaireResponse == null)
        {
            var newResponse = new SaveQuestionnaireResponseDto
            {
                QuestionnaireId = Questionnaire.Id,
                Items = Questionnaire.Items.Select(s => s.ToSaveResponseDto()).ToList()
            };

            await QuestionnaireResponseChanged.InvokeAsync(newResponse);
        }
    }
    
    private async Task HandleAnswerChanged()
    {
        // Notify parent component about answer changes
        await OnAnswerChanged.InvokeAsync();

        // Force re-render of all components to re-evaluate EnableWhen conditions
        await InvokeAsync(StateHasChanged);
    }

    /// <summary>
    /// Public method to validate the form
    /// </summary>
    public async Task<bool> ValidateAsync()
    {
        await _form.Validate();
        return _form.IsValid;
    }

}
