@using TrellusElevate.Application.Features.QuestionnaireResponses.Models
@using TrellusElevate.Application.Features.Questionnaires.Models
@using TrellusElevate.Core.Domains.Questionnaires.Common.Helpers
@using TrellusElevate.Core.Domains.Questionnaires.Entities
@using TrellusElevate.Core.Domains.Questionnaires.Mappers
@using TrellusElevate.Core.Domains.Questionnaires.ValueObjects

@* Only render if the question should be visible based on EnableWhen conditions *@
@if (_isVisible)
{
    @* Handle Group type questions (nested questions) *@
    @if (Item.Type == QuestionType.Group)
    {
        <MudCard Class="mb-4" Elevation="2">
            <MudCardContent>
                @if (!string.IsNullOrWhiteSpace(Item.Text))
                {
                    <MudText Typo="Typo.h6" Class="mb-3">@Item.Text</MudText>
                }

                @* Render nested questions *@
                @if (Item.Items.Any())
                {
                    <MudStack Spacing="3">
                        @foreach (var childItem in Item.Items.OrderBy(x => x.OrderNumber))
                        {
                            var childRequestItem = RequestItem.Items.FirstOrDefault(r => r.QuestionnaireItemId == childItem.Id);
                            @if (childRequestItem != null)
                            {
                                <QuestionItemControl Item="childItem"
                                                     RequestItem="childRequestItem"
                                                     Disabled="@Disabled"
                                                     AllResponseItems="@AllResponseItems"
                                                     AllQuestionnaireItems="@AllQuestionnaireItems"
                                                     OnAnswerChanged="@OnAnswerChanged"/>
                            }
                        }
                    </MudStack>
                }
            </MudCardContent>
        </MudCard>
    }

    @* Handle Display type questions (informational text) *@
    @if (Item.Type == QuestionType.Display)
    {
        <MudAlert Severity="Severity.Info" Class="mb-3">
            @if (!string.IsNullOrWhiteSpace(Item.Prefix))
            {
                <strong>@Item.Prefix</strong>
            }
            @Item.Text
        </MudAlert>
    }

    @* Handle questions with predefined options (select/choice questions) *@
    @if (Item.Options.Count > 0 && Item.Type.IsAnswerableType() && ShouldShowInput())
    {
        <MudGrid>
            <MudItem md="6">
                @if (!string.IsNullOrWhiteSpace(Item.Prefix))
                {
                    <strong>@Item.Prefix</strong>
                }
                @Item.Text
                @if (Item.Required)
                {
                    <MudText Typo="Typo.caption" Color="Color.Error">*</MudText>
                }
            </MudItem>
            <MudItem md="6">
                @switch (Item.UiElementType)
                {
                    case UiElementType.Slider:
                        @if (Item.Type is QuestionType.Integer or QuestionType.Decimal && Item.Options.Any())
                        {
                            var currentValue = RequestItem.Answers.FirstOrDefault();
                            var currentIndex = currentValue != null ? Item.Options.FindIndex(o => o.Answer?.Equals(currentValue) == true) : -1;
                            <MudSlider T="int"
                                       Value="@currentIndex"
                                       ValueChanged="@(index => SetSliderValue(index))"
                                       Min="0"
                                       Max="@(Item.Options.Count - 1)"
                                       Step="1"
                                       Disabled="@(Disabled || ShouldDisableInput())"
                                       TickMarks="true"
                                       TickMarkLabels="@(Item.Options.Select(o => o.Title ?? o.Answer?.Value?.ToString() ?? "").ToArray())"/>
                        }

                        break;

                    case UiElementType.List:
                        @if (Item.Repeats)
                        {
                            @* Multi-select checkboxes *@
                            <MudStack Spacing="1">
                                @foreach (var option in Item.Options)
                                {
                                    var isSelected = RequestItem.Answers.Any(a => a.Equals(option.Answer));
                                    <MudCheckBox T="bool"
                                                 Value="@isSelected"
                                                 ValueChanged="@(val => ToggleListOption(option.Answer!, val))"
                                                 Label="@option.Title"
                                                 Disabled="@(Disabled || ShouldDisableInput())"/>
                                }
                            </MudStack>
                        }
                        else
                        {
                            @* Single-select radio buttons *@
                            <MudRadioGroup T="QuestionAnswer?"
                                           Value="@RequestItem.Answers.FirstOrDefault()"
                                           ValueChanged="@(val => SetRadioValue(val))">
                                @foreach (var option in Item.Options)
                                {
                                    <MudRadio T="QuestionAnswer?"
                                              Value="@option.Answer"
                                              Disabled="@(Disabled || ShouldDisableInput())">
                                        @option.Title
                                    </MudRadio>
                                }
                            </MudRadioGroup>
                        }

                        break;

                    case UiElementType.Default:
                    default:
                        @if (Item.Repeats)
                        {
                            @* Multi-select dropdown *@
                            <MudSelect T="QuestionAnswer"
                                       Required="@Item.Required"
                                       Clearable="true"
                                       MultiSelection="true"
                                       SelectedValues="@RequestItem.Answers"
                                       SelectedValuesChanged="SetMultiSelectValue"
                                       MultiSelectionTextFunc="@GetMultiSelectionDisplayText"
                                       ToStringFunc="@(answer => GetOptionTitle(answer))"
                                       Disabled="@(Disabled || ShouldDisableInput())">
                                @foreach (var option in Item.Options)
                                {
                                    <MudSelectItem T="QuestionAnswer"
                                                   Value="option.Answer!">@option.Title</MudSelectItem>
                                }
                            </MudSelect>
                        }
                        else
                        {
                            @* Single select dropdown *@
                            <MudSelect T="List<QuestionAnswer>"
                                       Required="@Item.Required"
                                       Value="@RequestItem.Answers"
                                       ValueChanged="SetSelectValue"
                                       Clearable="true"
                                       ToStringFunc="@(ans => ans != null ? Item.Options.FirstOrDefault(s => s.Answer == ans.FirstOrDefault())?.Title : "")"
                                       Disabled="@(Disabled || ShouldDisableInput())">
                                @foreach (var option in Item.Options)
                                {
                                    <MudSelectItem T="List<QuestionAnswer>"
                                                   Value="[option.Answer!]">@option.Title</MudSelectItem>
                                }
                            </MudSelect>
                        }

                        break;
                }
            </MudItem>
        </MudGrid>
    }

    @* Handle answerable questions without predefined options *@
    @if (Item.Options.Count == 0 && Item.Type.IsAnswerableType() && ShouldShowInput())
    {
        var answerType = Item.Type.ToAnswerType();

        <MudGrid>
            <MudItem md="6">
                @if (!string.IsNullOrWhiteSpace(Item.Prefix))
                {
                    <strong>@Item.Prefix</strong>
                }
                @Item.Text
                @if (Item.Required)
                {
                    <MudText Typo="Typo.caption" Color="Color.Error">*</MudText>
                }
            </MudItem>
            <MudItem md="6">
                @switch (answerType)
                {
                    case AnswerType.Boolean:
                        <MudCheckBox T="bool?"
                                     Value="@(RequestItem.Answers.FirstOrDefault()?.GetValue<bool?>())"
                                     TriState="true"
                                     Required="@Item.Required"
                                     Disabled="@(Disabled || ShouldDisableInput())"
                                     ValueChanged="@(val => SetValue(val, answerType))"/>
                        break;

                    case AnswerType.Decimal:
                        <MudNumericField
                            T="decimal?"
                            Value="@(RequestItem.Answers.FirstOrDefault()?.GetValue<decimal?>())"
                            ValueChanged="@(val => SetValue(val, answerType))"
                            Disabled="@(Disabled || ShouldDisableInput())"
                            Clearable="true"
                            Required="@Item.Required"/>
                        break;

                    case AnswerType.Integer:
                        <MudNumericField
                            T="int?"
                            Value="@(RequestItem.Answers.FirstOrDefault()?.GetValue<int?>())"
                            ValueChanged="@(val => SetValue(val, answerType))"
                            Disabled="@(Disabled || ShouldDisableInput())"
                            Clearable="true"
                            Required="@Item.Required"/>
                        break;

                    case AnswerType.Date:
                        <MudDateWheelPicker Value="@GetDateValue()"
                                            ValueChanged="val => SetDateValue(val)"
                                            DateView="DateView.Date"
                                            Disabled="@(Disabled || ShouldDisableInput())"
                                            Clearable="true"
                                            Required="@Item.Required"/>
                        break;

                    case AnswerType.DateTime:
                        <MudDateWheelPicker Value="@(RequestItem.Answers.FirstOrDefault()?.GetValue<DateTime?>())"
                                            ValueChanged="val => SetValue(val, answerType)"
                                            DateView="DateView.Both"
                                            DateFormat="MM/dd/yyyy hh:mm"
                                            Disabled="@(Disabled || ShouldDisableInput())"
                                            Clearable="true"
                                            Required="@Item.Required"/>
                        break;

                    case AnswerType.Time:
                        <MudDateWheelPicker
                            Value="@GetTimeValue()"
                            ValueChanged="val => SetTimeValue(val)"
                            DateView="DateView.Time"
                            DateFormat="hh:mm"
                            Disabled="@(Disabled || ShouldDisableInput())"
                            Clearable="true"
                            Required="@Item.Required"/>
                        break;

                    case AnswerType.String:
                        <MudTextField
                            T="string"
                            Value="@(RequestItem.Answers.FirstOrDefault()?.GetValue<string>())"
                            ValueChanged="val => SetValue(val, answerType)"
                            Disabled="@(Disabled || ShouldDisableInput())"
                            Required="@Item.Required"
                            Clearable="true"
                            MaxLength="@(Item.MaxLength ?? 524288)"/>
                        break;

                    case AnswerType.Text:
                        <MudTextField
                            T="string"
                            Value="@(RequestItem.Answers.FirstOrDefault()?.GetValue<string>())"
                            ValueChanged="val => SetValue(val, answerType)"
                            Disabled="@(Disabled || ShouldDisableInput())"
                            Lines="3"
                            Required="@Item.Required"
                            Clearable="true"
                            MaxLength="@(Item.MaxLength ?? 524288)"/>
                        break;

                    case AnswerType.Url:
                        <MudTextField
                            T="string"
                            Value="@(RequestItem.Answers.FirstOrDefault()?.GetValue<string>())"
                            ValueChanged="val => SetValue(val, answerType)"
                            Disabled="@(Disabled || ShouldDisableInput())"
                            Required="@Item.Required"
                            Adornment="Adornment.Start"
                            AdornmentIcon="@Icons.Material.Filled.Link"
                            Clearable="true"
                            MaxLength="@(Item.MaxLength ?? 524288)"/>
                        break;

                    default:
                        <MudAlert Severity="Severity.Warning">
                            <MudText>Question type '@answerType' is not supported yet.</MudText>
                        </MudAlert>
                        break;
                }
            </MudItem>
        </MudGrid>
    }
}

@code {
    [Parameter] public QuestionnaireItemDto Item { get; set; } = null!;
    [Parameter] public SaveQuestionnaireResponseItemDto RequestItem { get; set; } = null!;
    [Parameter] public bool Disabled { get; set; }
    [Parameter] public List<SaveQuestionnaireResponseItemDto> AllResponseItems { get; set; } = [];
    [Parameter] public List<QuestionnaireItemDto> AllQuestionnaireItems { get; set; } = [];
    [Parameter] public EventCallback OnAnswerChanged { get; set; }

    private bool _isVisible = true;

    protected override void OnInitialized()
    {
        base.OnInitialized();
        UpdateVisibility();
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        UpdateVisibility();
    }

    private async Task SetValue(object? value, AnswerType type)
    {
        RequestItem.Answers = value != null ? [new QuestionAnswer(value, type)] : [];

        // Notify parent that an answer has changed so other questions can re-evaluate their EnableWhen conditions
        await OnAnswerChanged.InvokeAsync();
    }

    private DateTime? GetDateValue()
    {
        var dateOnly = RequestItem.Answers.FirstOrDefault()?.GetValue<DateOnly?>();
        return dateOnly?.ToDateTime(TimeOnly.MinValue);
    }

    private async Task SetDateValue(DateTime? value)
    {
        DateOnly? dateOnlyValue = value.HasValue ? DateOnly.FromDateTime(value.Value) : null;
        await SetValue(dateOnlyValue, AnswerType.Date);
    }

    // Time helper methods
    private DateTime? GetTimeValue()
    {
        var timeOnly = RequestItem.Answers.FirstOrDefault()?.GetValue<TimeOnly?>();
        return timeOnly.HasValue ? DateTime.Today.Add(timeOnly.Value.ToTimeSpan()) : null;
    }

    private async Task SetTimeValue(DateTime? value)
    {
        TimeOnly? timeOnlyValue = value.HasValue ? TimeOnly.FromDateTime(value.Value) : null;
        await SetValue(timeOnlyValue, AnswerType.Time);
    }

    private async Task SetSelectValue(List<QuestionAnswer> value)
    {
        RequestItem.Answers = value;

        // Notify parent that an answer has changed so other questions can re-evaluate their EnableWhen conditions
        await OnAnswerChanged.InvokeAsync();
    }

    private async Task SetMultiSelectValue(IEnumerable<QuestionAnswer?>? values)
    {
        RequestItem.Answers = values?.Where(s => s != null).Select(s => s!).ToList() ?? [];

        // Notify parent that an answer has changed so other questions can re-evaluate their EnableWhen conditions
        await OnAnswerChanged.InvokeAsync();
    }

    /// <summary>
    /// Gets the display text for multi-selection dropdown
    /// </summary>
    /// <param name="selectedStringValues">List of string representations of selected values</param>
    /// <returns>Display text for the multi-select dropdown</returns>
    private string GetMultiSelectionDisplayText(List<string>? selectedStringValues)
    {
        if (selectedStringValues == null || !selectedStringValues.Any())
            return "";

        return selectedStringValues.Count switch
        {
            1 => selectedStringValues.First(),
            2 => string.Join(", ", selectedStringValues),
            3 => string.Join(", ", selectedStringValues),
            _ => $"{string.Join(", ", selectedStringValues.Take(2))}, +{selectedStringValues.Count - 2} more"
        };
    }

    /// <summary>
    /// Gets the title/display text for a specific answer option
    /// </summary>
    /// <param name="answer">The question answer</param>
    /// <returns>The title of the matching option or empty string</returns>
    private string GetOptionTitle(QuestionAnswer? answer)
    {
        if (answer == null) return "";

        var option = Item.Options.FirstOrDefault(o => o.Answer?.Equals(answer) == true);
        return option?.Title ?? "";
    }

    /// <summary>
    /// Updates the visibility state based on EnableWhen conditions
    /// </summary>
    private void UpdateVisibility()
    {
        var newVisibility = CalculateVisibility();
        if (_isVisible != newVisibility)
        {
            _isVisible = newVisibility;
            StateHasChanged();
        }
    }

    /// <summary>
    /// Public method to trigger visibility update from parent components
    /// </summary>
    public void RefreshVisibility()
    {
        UpdateVisibility();
    }

    /// <summary>
    /// Calculates if this question should be shown based on EnableWhen conditions
    /// </summary>
    private bool CalculateVisibility()
    {
        // If no EnableWhen conditions, always show the question
        if (!Item.EnableWhen.Any())
        {
            return true;
        }

        // Evaluate each EnableWhen condition
        var conditionResults = new List<bool>();

        foreach (var enableWhen in Item.EnableWhen)
        {
            var conditionMet = EvaluateEnableWhenCondition(enableWhen);
            conditionResults.Add(conditionMet);
        }

        // Apply the EnableBehavior logic (All vs Any)
        return Item.EnableBehavior switch
        {
            EnableWhenBehavior.All => conditionResults.All(result => result),
            EnableWhenBehavior.Any => conditionResults.Any(result => result),
            _ => conditionResults.All(result => result) // Default to All if not specified
        };
    }

    /// <summary>
    /// Determines if input controls should be shown based on EnableWhen conditions and DisabledDisplay setting
    /// </summary>
    private bool ShouldShowInput()
    {
        // If DisabledDisplay is set to Hidden, hide input regardless of EnableWhen
        if (Item.DisabledDisplay == DisabledDisplay.Hidden)
        {
            return false;
        }

        // If EnableWhen conditions are not satisfied, hide input (unless DisabledDisplay overrides)
        if (!CalculateVisibility())
        {
            return false;
        }

        // Show input in all other cases
        return true;
    }

    /// <summary>
    /// Determines if input controls should be disabled based on ReadOnly property and DisabledDisplay setting
    /// </summary>
    private bool ShouldDisableInput()
    {
        // If the question is marked as ReadOnly, always disable input
        if (Item.ReadOnly)
        {
            return true;
        }

        // If DisabledDisplay is set to Protected, disable input regardless of EnableWhen
        if (Item.DisabledDisplay == DisabledDisplay.Protected)
        {
            return true;
        }

        // Don't disable in other cases
        return false;
    }

    /// <summary>
    /// Evaluates a single EnableWhen condition
    /// </summary>
    private bool EvaluateEnableWhenCondition(EnableWhen enableWhen)
    {
        if (string.IsNullOrEmpty(enableWhen.LinkId))
        {
            return false;
        }

        // Find the question that this condition references by LinkId
        var referencedQuestion = FindQuestionByLinkId(enableWhen.LinkId);
        if (referencedQuestion == null)
        {
            return false;
        }

        // Find the current answer for the referenced question
        var referencedAnswer = FindAnswerByQuestionId(referencedQuestion.Id);

        // If no answer exists, handle based on operator
        if (referencedAnswer == null)
        {
            return enableWhen.Operator != Operator.Exists && enableWhen.Answer == null;
        }

        // Use the static IsSatisfiedBy method to evaluate the condition
        return QuestionAnswer.IsSatisfiedBy([referencedAnswer], [enableWhen.Answer!], enableWhen.Operator);
    }

    /// <summary>
    /// Finds a questionnaire item by its LinkId
    /// </summary>
    private QuestionnaireItemDto? FindQuestionByLinkId(string linkId)
    {
        return AllQuestionnaireItems.FirstOrDefault(q => q.LinkId == linkId);
    }

    /// <summary>
    /// Finds the current answer for a question by its ID
    /// </summary>
    private QuestionAnswer? FindAnswerByQuestionId(Guid questionId)
    {
        var responseItem = FindResponseItemByQuestionId(AllResponseItems, questionId);
        return responseItem?.Answers.FirstOrDefault();
    }

    /// <summary>
    /// Recursively searches for a response item by question ID
    /// </summary>
    private SaveQuestionnaireResponseItemDto? FindResponseItemByQuestionId(List<SaveQuestionnaireResponseItemDto> items, Guid questionId)
    {
        foreach (var item in items)
        {
            if (item.QuestionnaireItemId == questionId)
            {
                return item;
            }

            // Search in nested items
            var nestedResult = FindResponseItemByQuestionId(item.Items, questionId);
            if (nestedResult != null)
            {
                return nestedResult;
            }
        }

        return null;
    }

    /// <summary>
    /// Sets value from slider control
    /// </summary>
    private async Task SetSliderValue(int index)
    {
        if (index >= 0 && index < Item.Options.Count)
        {
            RequestItem.Answers = [Item.Options[index].Answer!];
            await OnAnswerChanged.InvokeAsync();
        }
    }

    /// <summary>
    /// Toggles an option in list control (for multi-select)
    /// </summary>
    private async Task ToggleListOption(QuestionAnswer option, bool isSelected)
    {
        var currentAnswers = RequestItem.Answers.ToList();

        if (isSelected)
        {
            if (!currentAnswers.Any(a => a.Equals(option)))
            {
                currentAnswers.Add(option);
            }
        }
        else
        {
            currentAnswers.RemoveAll(a => a.Equals(option));
        }

        RequestItem.Answers = currentAnswers;
        await OnAnswerChanged.InvokeAsync();
    }

    /// <summary>
    /// Sets value from radio button control
    /// </summary>
    private async Task SetRadioValue(QuestionAnswer? value)
    {
        RequestItem.Answers = value != null ? [value] : [];
        await OnAnswerChanged.InvokeAsync();
    }

}