@using System.Collections.Generic

<MudStack Spacing="2">
    <MudText Typo="Typo.subtitle2">Metadata (@_metadataItems.Count items)</MudText>

    <MudStack Spacing="1">
        @foreach (var item in _metadataItems.Select((value, index) => new { value, index }))
        {
            <MudPaper Elevation="1" Class="pa-3">
                <MudGrid Spacing="2">
                    <MudItem xs="12" sm="5">
                        <MudTextField @bind-Value="item.value.Key"
                                      @bind-Value:after="@(() => OnMetadataItemChanged())"
                                      Label="Key"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense"
                                      HelperText="Metadata key"/>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="item.value.Value"
                                      @bind-Value:after="@(() => OnMetadataItemChanged())"
                                      Label="Value"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense"
                                      HelperText="Metadata value"/>
                    </MudItem>
                    <MudItem xs="12" sm="1" Class="d-flex align-center justify-center">
                        <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                       Color="Color.Error"
                                       Size="Size.Small"
                                       OnClick="@(() => RemoveMetadataItem(item.index))"/>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        }
    </MudStack>

    <MudButton Variant="Variant.Outlined"
               Color="Color.Primary"
               StartIcon="@Icons.Material.Filled.Add"
               OnClick="@AddMetadataItem">
        Add Metadata
    </MudButton>
</MudStack>

@code {
    [Parameter] public Dictionary<string, object>? Metadata { get; set; }
    [Parameter] public EventCallback<Dictionary<string, object>?> MetadataChanged { get; set; }

    private List<MetadataItem> _metadataItems = new();

    protected override void OnParametersSet()
    {
        base.OnParametersSet();

        // Convert Dictionary to List for easier editing
        _metadataItems = Metadata?.Select(kvp => new MetadataItem
        {
            Key = kvp.Key,
            Value = kvp.Value?.ToString() ?? ""
        }).ToList() ?? new List<MetadataItem>();
    }

    private void AddMetadataItem()
    {
        _metadataItems.Add(new MetadataItem { Key = "", Value = "" });
        StateHasChanged();
        _ = Task.Run(async () => await OnMetadataChanged());
    }

    private void RemoveMetadataItem(int index)
    {
        if (index >= 0 && index < _metadataItems.Count)
        {
            _metadataItems.RemoveAt(index);
            StateHasChanged();
            _ = Task.Run(async () => await OnMetadataChanged());
        }
    }

    private void OnMetadataItemChanged()
    {
        _ = Task.Run(async () => await OnMetadataChanged());
    }

    private async Task OnMetadataChanged()
    {
        // Convert List back to Dictionary, filtering out empty keys
        var metadata = _metadataItems
            .Where(item => !string.IsNullOrWhiteSpace(item.Key))
            .ToDictionary(item => item.Key, item => (object)item.Value);

        // Update the Metadata parameter directly
        Metadata = metadata.Any() ? metadata : null;

        // Also invoke the callback if provided (for backward compatibility)
        if (MetadataChanged.HasDelegate)
        {
            await MetadataChanged.InvokeAsync(Metadata);
        }
    }

    private class MetadataItem
    {
        public string Key { get; set; } = "";
        public string Value { get; set; } = "";
    }
}
