@using System.Collections.Generic

<MudStack Spacing="2">
    <MudText Typo="Typo.subtitle2">Metadata</MudText>
    
    @if (_metadataItems.Any())
    {
        <MudStack Spacing="1">
            @for (var i = 0; i < _metadataItems.Count; i++)
            {
                var index = i; // Capture for closure
                var item = _metadataItems[i];
                
                <MudPaper Elevation="1" Class="pa-3">
                    <MudGrid Spacing="2">
                        <MudItem xs="12" sm="5">
                            <MudTextField Value="@item.Key"
                                          ValueChanged="@(EventCallback.Factory.Create<string>(this, value => OnKeyChanged(index, value)))"
                                          Label="Key"
                                          Variant="Variant.Outlined"
                                          Margin="Margin.Dense"
                                          Immediate="true"
                                          HelperText="Metadata key"/>
                        </MudItem>
                        <MudItem xs="12" sm="6">
                            <MudTextField Value="@item.Value"
                                          ValueChanged="@(EventCallback.Factory.Create<string>(this, value => OnValueChanged(index, value)))"
                                          Label="Value"
                                          Variant="Variant.Outlined"
                                          Margin="Margin.Dense"
                                          Immediate="true"
                                          HelperText="Metadata value"/>
                        </MudItem>
                        <MudItem xs="12" sm="1" Class="d-flex align-center justify-center">
                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                           Color="Color.Error"
                                           Size="Size.Small"
                                           OnClick="@(async () => await RemoveMetadataItem(index))"/>
                        </MudItem>
                    </MudGrid>
                </MudPaper>
            }
        </MudStack>
    }
    
    <MudButton Variant="Variant.Outlined"
               Color="Color.Primary"
               StartIcon="@Icons.Material.Filled.Add"
               OnClick="@(async () => await AddMetadataItem())">
        Add Metadata
    </MudButton>
</MudStack>

@code {
    [Parameter] public Dictionary<string, object>? Metadata { get; set; }
    [Parameter] public EventCallback<Dictionary<string, object>?> MetadataChanged { get; set; }

    private List<MetadataItem> _metadataItems = new();

    protected override void OnParametersSet()
    {
        base.OnParametersSet();

        // Convert Dictionary to List for easier editing
        _metadataItems = Metadata?.Select(kvp => new MetadataItem
        {
            Key = kvp.Key,
            Value = kvp.Value?.ToString() ?? ""
        }).ToList() ?? new List<MetadataItem>();
    }

    private async Task AddMetadataItem()
    {
        _metadataItems.Add(new MetadataItem { Key = "", Value = "" });
        await OnMetadataChanged();
    }

    private async Task RemoveMetadataItem(int index)
    {
        if (index >= 0 && index < _metadataItems.Count)
        {
            _metadataItems.RemoveAt(index);
            await OnMetadataChanged();
        }
    }

    private async Task OnKeyChanged(int index, string value)
    {
        if (index >= 0 && index < _metadataItems.Count)
        {
            _metadataItems[index].Key = value ?? "";
            await OnMetadataChanged();
        }
    }

    private async Task OnValueChanged(int index, string value)
    {
        if (index >= 0 && index < _metadataItems.Count)
        {
            _metadataItems[index].Value = value ?? "";
            await OnMetadataChanged();
        }
    }

    private async Task OnMetadataChanged()
    {
        // Convert List back to Dictionary, filtering out empty keys
        var metadata = _metadataItems
            .Where(item => !string.IsNullOrWhiteSpace(item.Key))
            .ToDictionary(item => item.Key, item => (object)item.Value);

        await MetadataChanged.InvokeAsync(metadata.Any() ? metadata : null);
    }

    private class MetadataItem
    {
        public string Key { get; set; } = "";
        public string Value { get; set; } = "";
    }
}
