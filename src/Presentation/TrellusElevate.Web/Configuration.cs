using TrellusElevate.Web.Components.Pages.Patients.Patient;
using TrellusElevate.Web.Components.Pages.Administration.Questionnaires;

namespace TrellusElevate.Web;

public static class Configuration
{
    public static IServiceCollection AddWeb(this IServiceCollection services)
    {
        ConfigureMudDefaults();
        return services.AddState();
    }

    public static IServiceCollection AddState(this IServiceCollection services)
    {
        services.AddScoped<PatientState>();
        services.AddScoped<QuestionnaireState>();
        return services;
    }
    
    public static void ConfigureMudDefaults()
    {
        MudBlazor.MudGlobal.InputDefaults.Margin = MudBlazor.Margin.Dense;
        MudBlazor.MudGlobal.InputDefaults.Variant = MudBlazor.Variant.Outlined;
        MudBlazor.MudGlobal.InputDefaults.ShrinkLabel = true;
    }
}