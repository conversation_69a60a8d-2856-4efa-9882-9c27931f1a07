using FluentValidation;

namespace TrellusElevate.Web.Common.Extensions;

public static class ValidatorExtensions
{
    /// <summary>
    /// Converts a validator for T into a MudForm-compatible validation function.
    /// </summary>
    public static Func<object, object, Task<IEnumerable<string>>> AsMudFormValidation<T>(
        this AbstractValidator<T> validator)
    {
        return async (model, propertyName) =>
        {
            // Cast the model back to T
            var typedModel = (T)model;
            var typedPropertyName = (string)propertyName;

            // Perform your property-level validation
            var result = await validator.ValidateAsync(
                ValidationContext<T>.CreateWithOptions(typedModel, x => x.IncludeProperties(typedPropertyName)));

            return result.IsValid
                ? []
                : result.Errors.Select(e => e.ErrorMessage);
        };
    }
}