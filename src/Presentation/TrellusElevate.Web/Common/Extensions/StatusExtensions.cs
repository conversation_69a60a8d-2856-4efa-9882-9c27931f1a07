using MudBlazor;
using TrellusElevate.Core.Domains.Assessments.Entities;
using TrellusElevate.Core.Domains.Patients.Entities;
using TrellusElevate.Core.Domains.Questionnaires.Entities;
using TrellusElevate.Core.Domains.QuestionnaireResponses.Entities;

namespace TrellusElevate.Web.Common.Extensions;

public static class StatusExtensions
{
    public static Color GetColor(this PatientAssessmentStatus status) => status switch
    {
        PatientAssessmentStatus.Pending => Color.Primary,
        PatientAssessmentStatus.Cancelled => Color.Error,
        PatientAssessmentStatus.Skipped => Color.Warning,
        PatientAssessmentStatus.Completed => Color.Success,
        _ => Color.Default
    };

    public static Color GetColor(this PatientStatus status) => status switch
    {
        PatientStatus.PreRegistered => Color.Primary,
        PatientStatus.Registered => Color.Success,
        PatientStatus.Paused => Color.Info,
        PatientStatus.Discontinued => Color.Error,
        PatientStatus.Archived => Color.Secondary,
        _ => Color.Default
    };

    public static Color GetColor(this AssessmentStatus status) => status switch
    {
        AssessmentStatus.Draft => Color.Primary,
        AssessmentStatus.Active => Color.Success,
        AssessmentStatus.Inactive => Color.Error,
        _ => Color.Default
    };

    public static Color GetColor(this AssessmentScoringProfileStatus status) => status switch
    {
        AssessmentScoringProfileStatus.Draft => Color.Primary,
        AssessmentScoringProfileStatus.Active => Color.Success,
        AssessmentScoringProfileStatus.Inactive => Color.Error,
        _ => Color.Default
    };

    public static Color GetColor(this QuestionnaireStatus status) => status switch
    {
        QuestionnaireStatus.Draft => Color.Primary,
        QuestionnaireStatus.Active => Color.Success,
        QuestionnaireStatus.Inactive => Color.Error,
        _ => Color.Default
    };

    public static Color GetColor(this QuestionnaireResponseStatus status) => status switch
    {
        QuestionnaireResponseStatus.InProgress => Color.Primary,
        QuestionnaireResponseStatus.Completed => Color.Success,
        QuestionnaireResponseStatus.Amended => Color.Info,
        QuestionnaireResponseStatus.Stopped => Color.Error,
        _ => Color.Default
    };
}