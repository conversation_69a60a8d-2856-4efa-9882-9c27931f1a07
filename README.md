## Setup local env

Postgres

```
docker run --name postgres \
  -e POSTGRES_PASSWORD=postgres \
  -v "$(pwd)/data:/var/lib/postgresql/data" \
  -p 5432:5432 \
  -d postgres
```

### Migrations
1. Navigate to src folder
2. Create migration: ``` dotnet ef migrations add Initial1 -p Infrastructure/TrellusElevate.Infrastructure -s Presentation/TrellusElevate.Api -o Data/Migrations ```
3. Update database: ``` dotnet ef database update -p Infrastructure/TrellusElevate.Infrastructure -s Presentation/TrellusElevate.Api ```
